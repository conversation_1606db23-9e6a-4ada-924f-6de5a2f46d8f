<!DOCTYPE html>
<html lang="zh-CN">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>分类页面骨架屏演示</title>
    <style>
        * {
            margin: 0;
            padding: 0;
            box-sizing: border-box;
        }

        body {
            font-family: -apple-system, BlinkMacSystemFont, 'Segoe UI', Roboto, sans-serif;
            background-color: #f8f8f8;
        }

        /* 转换rpx到px，1rpx = 0.5px */
        .skeleton {
            height: 100vh;
            display: flex;
            flex-direction: column;
            background-color: #f8f8f8;
        }

        /* 搜索头部骨架 */
        .skeleton-header {
            height: 44px; /* 88rpx */
            background-color: #129799;
            padding: 0 10px; /* 20rpx */
            display: flex;
            align-items: center;
        }

        .skeleton-search-input {
            flex: 1;
            height: 34px; /* 68rpx */
            background: linear-gradient(90deg, #f0f0f0 25%, #e0e0e0 50%, #f0f0f0 75%);
            background-size: 200% 100%;
            border-radius: 17px; /* 34rpx */
            animation: loading 1.5s infinite;
        }

        @keyframes loading {
            0% {
                background-position: 200% 0;
            }
            100% {
                background-position: -200% 0;
            }
        }

        /* Tab标签栏骨架 */
        .skeleton-tabs {
            height: 44px; /* 88rpx */
            background-color: #fff;
            border-bottom: 0.5px solid #eee; /* 1rpx */
            display: flex;
            align-items: center;
            padding: 0 10px; /* 20rpx */
        }

        .skeleton-tab-item {
            width: 60px; /* 120rpx */
            height: 20px; /* 40rpx */
            background: linear-gradient(90deg, #f0f0f0 25%, #e0e0e0 50%, #f0f0f0 75%);
            background-size: 200% 100%;
            border-radius: 10px; /* 20rpx */
            margin-right: 15px; /* 30rpx */
            animation: loading 1.5s infinite;
        }

        .skeleton-tab-item:nth-child(1) { animation-delay: 0s; }
        .skeleton-tab-item:nth-child(2) { animation-delay: 0.1s; }
        .skeleton-tab-item:nth-child(3) { animation-delay: 0.2s; }

        /* 主内容区域骨架 */
        .skeleton-main {
            flex: 1;
            display: flex;
        }

        /* 左侧导航骨架 */
        .skeleton-nav {
            width: 103px; /* 206rpx */
            background-color: #f6f6f6;
            padding: 15px 0; /* 30rpx */
        }

        .skeleton-nav-item {
            margin-bottom: 15px; /* 30rpx */
            display: flex;
            justify-content: center;
        }

        .skeleton-nav-title {
            width: 83px; /* 166rpx */
            height: 32.5px; /* 65rpx */
            background: linear-gradient(90deg, #f0f0f0 25%, #e0e0e0 50%, #f0f0f0 75%);
            background-size: 200% 100%;
            border-radius: 5px; /* 10rpx */
            animation: loading 1.5s infinite;
        }

        .skeleton-nav-item:nth-child(1) .skeleton-nav-title { animation-delay: 0s; }
        .skeleton-nav-item:nth-child(2) .skeleton-nav-title { animation-delay: 0.05s; }
        .skeleton-nav-item:nth-child(3) .skeleton-nav-title { animation-delay: 0.1s; }
        .skeleton-nav-item:nth-child(4) .skeleton-nav-title { animation-delay: 0.15s; }
        .skeleton-nav-item:nth-child(5) .skeleton-nav-title { animation-delay: 0.2s; }
        .skeleton-nav-item:nth-child(6) .skeleton-nav-title { animation-delay: 0.25s; }
        .skeleton-nav-item:nth-child(7) .skeleton-nav-title { animation-delay: 0.3s; }
        .skeleton-nav-item:nth-child(8) .skeleton-nav-title { animation-delay: 0.35s; }

        /* 右侧商品列表骨架 */
        .skeleton-content {
            flex: 1;
            background-color: #fff;
            padding: 10px; /* 20rpx */
        }

        .skeleton-product-item {
            display: flex;
            padding: 10px; /* 20rpx */
            margin-bottom: 10px; /* 20rpx */
            background-color: #fff;
            border-radius: 10px; /* 20rpx */
            box-shadow: 0 2px 6px 0.5px rgba(3, 3, 3, 0.06); /* 0 4rpx 12rpx 1rpx */
        }

        .skeleton-product-image {
            width: 80px; /* 160rpx */
            height: 80px; /* 160rpx */
            background: linear-gradient(90deg, #f0f0f0 25%, #e0e0e0 50%, #f0f0f0 75%);
            background-size: 200% 100%;
            border-radius: 5px; /* 10rpx */
            animation: loading 1.5s infinite;
            flex-shrink: 0;
        }

        .skeleton-product-info {
            flex: 1;
            margin-left: 10px; /* 20rpx */
            display: flex;
            flex-direction: column;
            justify-content: space-between;
        }

        .skeleton-product-title {
            width: 100%;
            height: 16px; /* 32rpx */
            background: linear-gradient(90deg, #f0f0f0 25%, #e0e0e0 50%, #f0f0f0 75%);
            background-size: 200% 100%;
            border-radius: 2px; /* 4rpx */
            animation: loading 1.5s infinite;
            margin-bottom: 6px; /* 12rpx */
        }

        .skeleton-product-title2 {
            width: 80%;
            height: 16px; /* 32rpx */
            background: linear-gradient(90deg, #f0f0f0 25%, #e0e0e0 50%, #f0f0f0 75%);
            background-size: 200% 100%;
            border-radius: 2px; /* 4rpx */
            animation: loading 1.5s infinite;
            margin-bottom: 10px; /* 20rpx */
        }

        .skeleton-product-price {
            display: flex;
            align-items: center;
        }

        .skeleton-price-text {
            width: 40px; /* 80rpx */
            height: 14px; /* 28rpx */
            background: linear-gradient(90deg, #f0f0f0 25%, #e0e0e0 50%, #f0f0f0 75%);
            background-size: 200% 100%;
            border-radius: 2px; /* 4rpx */
            animation: loading 1.5s infinite;
            margin-right: 5px; /* 10rpx */
        }

        .skeleton-price-tag {
            width: 30px; /* 60rpx */
            height: 12px; /* 24rpx */
            background: linear-gradient(90deg, #f0f0f0 25%, #e0e0e0 50%, #f0f0f0 75%);
            background-size: 200% 100%;
            border-radius: 4px; /* 8rpx */
            animation: loading 1.5s infinite;
        }

        /* 控制按钮 */
        .controls {
            position: fixed;
            top: 20px;
            right: 20px;
            z-index: 1000;
        }

        .btn {
            background: #007aff;
            color: white;
            border: none;
            padding: 10px 20px;
            border-radius: 5px;
            cursor: pointer;
            margin-left: 10px;
        }

        .btn:hover {
            background: #0056b3;
        }

        .hidden {
            display: none;
        }

        .content {
            padding: 20px;
            background: white;
            margin: 20px;
            border-radius: 10px;
            text-align: center;
        }
    </style>
</head>
<body>
    <div class="controls">
        <button class="btn" onclick="showSkeleton()">显示骨架屏</button>
        <button class="btn" onclick="hideSkeleton()">隐藏骨架屏</button>
    </div>

    <!-- 骨架屏 -->
    <div id="skeleton" class="skeleton">
        <!-- 搜索头部骨架 -->
        <div class="skeleton-header">
            <div class="skeleton-search-input"></div>
        </div>

        <!-- Tab标签栏骨架 -->
        <div class="skeleton-tabs">
            <div class="skeleton-tab-item"></div>
            <div class="skeleton-tab-item"></div>
            <div class="skeleton-tab-item"></div>
        </div>

        <!-- 主内容区域骨架 -->
        <div class="skeleton-main">
            <!-- 左侧导航骨架 -->
            <div class="skeleton-nav">
                <div class="skeleton-nav-item">
                    <div class="skeleton-nav-title"></div>
                </div>
                <div class="skeleton-nav-item">
                    <div class="skeleton-nav-title"></div>
                </div>
                <div class="skeleton-nav-item">
                    <div class="skeleton-nav-title"></div>
                </div>
                <div class="skeleton-nav-item">
                    <div class="skeleton-nav-title"></div>
                </div>
                <div class="skeleton-nav-item">
                    <div class="skeleton-nav-title"></div>
                </div>
                <div class="skeleton-nav-item">
                    <div class="skeleton-nav-title"></div>
                </div>
                <div class="skeleton-nav-item">
                    <div class="skeleton-nav-title"></div>
                </div>
                <div class="skeleton-nav-item">
                    <div class="skeleton-nav-title"></div>
                </div>
            </div>

            <!-- 右侧商品列表骨架 -->
            <div class="skeleton-content">
                <div class="skeleton-product-item">
                    <div class="skeleton-product-image"></div>
                    <div class="skeleton-product-info">
                        <div class="skeleton-product-title"></div>
                        <div class="skeleton-product-title2"></div>
                        <div class="skeleton-product-price">
                            <div class="skeleton-price-text"></div>
                            <div class="skeleton-price-tag"></div>
                        </div>
                    </div>
                </div>
                <div class="skeleton-product-item">
                    <div class="skeleton-product-image"></div>
                    <div class="skeleton-product-info">
                        <div class="skeleton-product-title"></div>
                        <div class="skeleton-product-title2"></div>
                        <div class="skeleton-product-price">
                            <div class="skeleton-price-text"></div>
                            <div class="skeleton-price-tag"></div>
                        </div>
                    </div>
                </div>
                <div class="skeleton-product-item">
                    <div class="skeleton-product-image"></div>
                    <div class="skeleton-product-info">
                        <div class="skeleton-product-title"></div>
                        <div class="skeleton-product-title2"></div>
                        <div class="skeleton-product-price">
                            <div class="skeleton-price-text"></div>
                            <div class="skeleton-price-tag"></div>
                        </div>
                    </div>
                </div>
                <div class="skeleton-product-item">
                    <div class="skeleton-product-image"></div>
                    <div class="skeleton-product-info">
                        <div class="skeleton-product-title"></div>
                        <div class="skeleton-product-title2"></div>
                        <div class="skeleton-product-price">
                            <div class="skeleton-price-text"></div>
                            <div class="skeleton-price-tag"></div>
                        </div>
                    </div>
                </div>
                <div class="skeleton-product-item">
                    <div class="skeleton-product-image"></div>
                    <div class="skeleton-product-info">
                        <div class="skeleton-product-title"></div>
                        <div class="skeleton-product-title2"></div>
                        <div class="skeleton-product-price">
                            <div class="skeleton-price-text"></div>
                            <div class="skeleton-price-tag"></div>
                        </div>
                    </div>
                </div>
                <div class="skeleton-product-item">
                    <div class="skeleton-product-image"></div>
                    <div class="skeleton-product-info">
                        <div class="skeleton-product-title"></div>
                        <div class="skeleton-product-title2"></div>
                        <div class="skeleton-product-price">
                            <div class="skeleton-price-text"></div>
                            <div class="skeleton-price-tag"></div>
                        </div>
                    </div>
                </div>
            </div>
        </div>
    </div>

    <!-- 正常内容 -->
    <div id="content" class="content hidden">
        <h2>分类页面内容已加载</h2>
        <p>骨架屏已隐藏，显示正常页面内容</p>
    </div>

    <script>
        function showSkeleton() {
            document.getElementById('skeleton').classList.remove('hidden');
            document.getElementById('content').classList.add('hidden');
        }

        function hideSkeleton() {
            document.getElementById('skeleton').classList.add('hidden');
            document.getElementById('content').classList.remove('hidden');
        }

        // 页面加载时显示骨架屏，3秒后自动隐藏
        window.onload = function() {
            setTimeout(hideSkeleton, 3000);
        };
    </script>
</body>
</html>
