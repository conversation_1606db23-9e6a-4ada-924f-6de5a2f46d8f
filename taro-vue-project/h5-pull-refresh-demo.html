<!DOCTYPE html>
<html lang="zh-CN">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>H5下拉刷新演示</title>
    <style>
        * {
            margin: 0;
            padding: 0;
            box-sizing: border-box;
        }

        body {
            font-family: -apple-system, BlinkMacSystemFont, 'Segoe UI', Roboto, sans-serif;
            background-color: #f8f8f8;
            height: 100vh;
            overflow: hidden;
        }

        .container {
            display: flex;
            flex-direction: column;
            height: 100vh;
        }

        /* 头部样式 */
        .header {
            height: 44px;
            background-color: #129799;
            display: flex;
            align-items: center;
            justify-content: center;
            color: white;
            font-size: 18px;
            font-weight: bold;
        }

        /* Tab样式 */
        .tabs {
            height: 44px;
            background-color: #fff;
            border-bottom: 1px solid #eee;
            display: flex;
            align-items: center;
            padding: 0 10px;
        }

        .tab-item {
            padding: 8px 16px;
            margin-right: 10px;
            background: #f0f0f0;
            border-radius: 20px;
            font-size: 14px;
            color: #666;
        }

        .tab-item.active {
            background: #8CDAC6;
            color: #fff;
        }

        /* 滚动容器 */
        .scroll-container {
            flex: 1;
            position: relative;
            overflow-y: auto;
            -webkit-overflow-scrolling: touch;
        }

        /* 下拉刷新指示器 */
        .refresh-indicator {
            position: absolute;
            top: -60px;
            left: 0;
            right: 0;
            height: 60px;
            display: flex;
            align-items: center;
            justify-content: center;
            background-color: #f8f8f8;
            z-index: 10;
            transition: transform 0.3s ease, opacity 0.3s ease;
        }

        .refresh-icon {
            display: flex;
            align-items: center;
            justify-content: center;
            font-size: 14px;
            color: #666;
        }

        .refresh-loading {
            display: flex;
            align-items: center;
            justify-content: center;
            font-size: 14px;
            color: #129799;
        }

        .refresh-loading::before {
            content: '';
            width: 20px;
            height: 20px;
            border: 2px solid #129799;
            border-top-color: transparent;
            border-radius: 50%;
            animation: spin 1s linear infinite;
            margin-right: 8px;
        }

        @keyframes spin {
            0% { transform: rotate(0deg); }
            100% { transform: rotate(360deg); }
        }

        /* 内容区域 */
        .content {
            position: relative;
            min-height: 100%;
            padding: 10px;
        }

        .item {
            background: white;
            margin-bottom: 10px;
            padding: 15px;
            border-radius: 8px;
            box-shadow: 0 2px 4px rgba(0,0,0,0.1);
        }

        .item-title {
            font-size: 16px;
            font-weight: bold;
            margin-bottom: 8px;
            color: #333;
        }

        .item-desc {
            font-size: 14px;
            color: #666;
            line-height: 1.4;
        }

        .item-time {
            font-size: 12px;
            color: #999;
            margin-top: 8px;
        }

        /* 状态提示 */
        .status {
            position: fixed;
            top: 100px;
            right: 20px;
            background: rgba(0,0,0,0.8);
            color: white;
            padding: 10px;
            border-radius: 5px;
            font-size: 12px;
            z-index: 1000;
        }

        .controls {
            position: fixed;
            bottom: 20px;
            right: 20px;
            z-index: 1001;
        }

        .btn {
            background: #007aff;
            color: white;
            border: none;
            padding: 10px 15px;
            border-radius: 5px;
            cursor: pointer;
            margin-left: 5px;
            font-size: 12px;
        }
    </style>
</head>
<body>
    <div class="status" id="status">
        状态: 待机<br>
        下拉距离: 0px<br>
        刷新次数: 0
    </div>

    <div class="controls">
        <button class="btn" onclick="addItem()">添加项目</button>
        <button class="btn" onclick="clearItems()">清空</button>
    </div>

    <div class="container">
        <!-- 头部 -->
        <div class="header">
            H5下拉刷新演示
        </div>

        <!-- Tab -->
        <div class="tabs">
            <div class="tab-item active">全部</div>
            <div class="tab-item">最新</div>
            <div class="tab-item">热门</div>
        </div>

        <!-- 滚动容器 -->
        <div class="scroll-container" id="scrollContainer">
            <!-- 下拉刷新指示器 -->
            <div class="refresh-indicator" id="refreshIndicator">
                <div class="refresh-icon" id="refreshIcon">
                    <span>下拉刷新</span>
                </div>
                <div class="refresh-loading" id="refreshLoading" style="display: none;">
                    <span>正在刷新...</span>
                </div>
            </div>

            <!-- 内容区域 -->
            <div class="content" id="content">
                <div class="item">
                    <div class="item-title">欢迎使用H5下拉刷新</div>
                    <div class="item-desc">在这个演示页面中，您可以体验H5环境下的下拉刷新功能。向下拖拽页面即可触发刷新。</div>
                    <div class="item-time">2024-01-01 12:00:00</div>
                </div>
                <div class="item">
                    <div class="item-title">功能特点</div>
                    <div class="item-desc">• 流畅的下拉动画<br>• 阻尼效果<br>• 释放刷新提示<br>• 加载状态显示</div>
                    <div class="item-time">2024-01-01 11:30:00</div>
                </div>
                <div class="item">
                    <div class="item-title">使用说明</div>
                    <div class="item-desc">1. 在页面顶部向下拖拽<br>2. 看到"释放刷新"提示时松手<br>3. 等待刷新完成</div>
                    <div class="item-time">2024-01-01 11:00:00</div>
                </div>
            </div>
        </div>
    </div>

    <script>
        let touchStartY = 0;
        let touchMoveY = 0;
        let pullDistance = 0;
        let isPulling = false;
        let isRefreshing = false;
        let refreshCount = 0;

        const scrollContainer = document.getElementById('scrollContainer');
        const refreshIndicator = document.getElementById('refreshIndicator');
        const refreshIcon = document.getElementById('refreshIcon');
        const refreshLoading = document.getElementById('refreshLoading');
        const status = document.getElementById('status');
        const content = document.getElementById('content');

        function updateStatus() {
            const statusText = isRefreshing ? '刷新中' : (isPulling ? '下拉中' : '待机');
            status.innerHTML = `
                状态: ${statusText}<br>
                下拉距离: ${Math.round(pullDistance)}px<br>
                刷新次数: ${refreshCount}
            `;
        }

        function onTouchStart(e) {
            const touch = e.touches[0];
            touchStartY = touch.clientY;
            isPulling = false;
            
            // 检查是否在顶部
            if (scrollContainer.scrollTop <= 0) {
                isPulling = true;
            }
        }

        function onTouchMove(e) {
            if (!isPulling || isRefreshing) return;
            
            const touch = e.touches[0];
            touchMoveY = touch.clientY;
            pullDistance = touchMoveY - touchStartY;
            
            if (pullDistance > 0) {
                e.preventDefault();
                
                // 阻尼效果
                const dampingFactor = 0.5;
                const maxPullDistance = 100;
                const actualDistance = Math.min(pullDistance * dampingFactor, maxPullDistance);
                
                // 更新指示器位置和透明度
                const transform = actualDistance - 60;
                const opacity = Math.min(actualDistance / 60, 1);
                
                refreshIndicator.style.transform = `translateY(${transform}px)`;
                refreshIndicator.style.opacity = opacity;
                
                // 更新提示文字
                if (actualDistance >= 60) {
                    refreshIcon.innerHTML = '<span>释放刷新</span>';
                } else {
                    refreshIcon.innerHTML = '<span>下拉刷新</span>';
                }
                
                updateStatus();
            }
        }

        function onTouchEnd(e) {
            if (!isPulling || isRefreshing) return;
            
            const dampingFactor = 0.5;
            const actualDistance = Math.min(pullDistance * dampingFactor, 100);
            
            if (actualDistance >= 60) {
                triggerRefresh();
            } else {
                resetRefreshState();
            }
            
            isPulling = false;
            pullDistance = 0;
            updateStatus();
        }

        async function triggerRefresh() {
            isRefreshing = true;
            refreshCount++;
            
            refreshIndicator.style.transform = 'translateY(0px)';
            refreshIndicator.style.opacity = '1';
            refreshIcon.style.display = 'none';
            refreshLoading.style.display = 'flex';
            
            updateStatus();
            
            // 模拟刷新延迟
            await new Promise(resolve => setTimeout(resolve, 2000));
            
            // 添加新内容
            addItem();
            
            // 延迟重置状态
            setTimeout(() => {
                resetRefreshState();
            }, 500);
        }

        function resetRefreshState() {
            isRefreshing = false;
            refreshIndicator.style.transform = 'translateY(-60px)';
            refreshIndicator.style.opacity = '0';
            refreshIcon.style.display = 'flex';
            refreshLoading.style.display = 'none';
            refreshIcon.innerHTML = '<span>下拉刷新</span>';
            updateStatus();
        }

        function addItem() {
            const now = new Date();
            const timeStr = now.toLocaleString('zh-CN');
            const itemHtml = `
                <div class="item">
                    <div class="item-title">新增项目 #${Date.now()}</div>
                    <div class="item-desc">这是通过下拉刷新新增的内容项目，展示了H5下拉刷新的实际效果。</div>
                    <div class="item-time">${timeStr}</div>
                </div>
            `;
            content.insertAdjacentHTML('afterbegin', itemHtml);
        }

        function clearItems() {
            const items = content.querySelectorAll('.item');
            items.forEach((item, index) => {
                if (index > 2) { // 保留前3个示例项目
                    item.remove();
                }
            });
        }

        // 绑定事件
        scrollContainer.addEventListener('touchstart', onTouchStart, { passive: false });
        scrollContainer.addEventListener('touchmove', onTouchMove, { passive: false });
        scrollContainer.addEventListener('touchend', onTouchEnd, { passive: false });

        // 初始化状态
        updateStatus();
        resetRefreshState();
    </script>
</body>
</html>
