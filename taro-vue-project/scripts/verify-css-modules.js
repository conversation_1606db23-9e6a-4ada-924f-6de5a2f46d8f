#!/usr/bin/env node

/**
 * CSS模块化验证脚本
 * 验证样式文件从全局样式改为CSS模块的效果
 */

const fs = require('fs');
const path = require('path');

console.log('🔍 验证CSS模块化转换结果...\n');

// 检查目标文件
const files = [
  {
    name: '分类页面Vue文件',
    path: 'src/pages/classification/index.vue',
    type: 'vue'
  },
  {
    name: '分类页面CSS模块',
    path: 'src/pages/classification/index.module.less',
    type: 'css'
  },
  {
    name: '首页CSS模块',
    path: 'src/pages/index/index.module.less',
    type: 'css'
  },
  {
    name: 'CSS模块类型声明',
    path: 'src/types/css-modules.d.ts',
    type: 'types'
  }
];

const results = {};

files.forEach(file => {
  const filePath = path.join(process.cwd(), file.path);
  if (fs.existsSync(filePath)) {
    results[file.name] = {
      exists: true,
      content: fs.readFileSync(filePath, 'utf8'),
      size: fs.statSync(filePath).size
    };
  } else {
    results[file.name] = { exists: false };
  }
});

console.log('📋 CSS模块化验证清单:\n');

// 1. 检查文件存在性
console.log('1. 文件存在性检查:');
files.forEach(file => {
  const result = results[file.name];
  const status = result.exists ? '✅' : '❌';
  const size = result.exists ? `(${(result.size / 1024).toFixed(1)}KB)` : '';
  console.log(`   ${status} ${file.name}: ${result.exists ? '存在' : '缺失'} ${size}`);
});

// 2. 检查CSS模块导入
console.log('\n2. CSS模块导入检查:');
const vueContent = results['分类页面Vue文件']?.content || '';
const importChecks = [
  { name: 'CSS模块导入语法', pattern: /import styles from ["']\.\/index\.module\.less["']/, required: true },
  { name: '移除全局样式导入', pattern: /import ["']\.\/index\.less["']/, shouldNotExist: true },
  { name: 'styles对象使用', pattern: /:class="styles\.\w+"/, required: true },
  { name: '动态class绑定', pattern: /:class="\[[\s\S]*styles\.\w+[\s\S]*\]"/, required: true }
];

importChecks.forEach(check => {
  const found = check.pattern.test(vueContent);
  let status;
  if (check.shouldNotExist) {
    status = !found ? '✅' : '❌';
    console.log(`   ${status} ${check.name}: ${!found ? '已移除' : '仍存在'}`);
  } else {
    status = found ? '✅' : (check.required ? '❌' : '⚠️');
    console.log(`   ${status} ${check.name}: ${found ? '已实现' : '缺失'}`);
  }
});

// 3. 检查CSS模块语法
console.log('\n3. CSS模块语法检查:');
const cssContent = results['分类页面CSS模块']?.content || '';
const cssChecks = [
  { name: 'CSS模块注释', pattern: /\/\* .* CSS Modules \*\//, required: true },
  { name: '局部作用域类', pattern: /\.\w+\s*\{/, required: true },
  { name: '全局作用域标记', pattern: /:global\(/, required: true },
  { name: '嵌套选择器', pattern: /\.\w+\s*\{[\s\S]*\.\w+\s*\{/, required: true },
  { name: '混合函数使用', pattern: /\.flex\(/, required: true },
  { name: '变量引用', pattern: /@\w+/, required: true }
];

cssChecks.forEach(check => {
  const found = check.pattern.test(cssContent);
  const status = found ? '✅' : (check.required ? '❌' : '⚠️');
  console.log(`   ${status} ${check.name}: ${found ? '已实现' : '缺失'}`);
});

// 4. 检查模板class使用
console.log('\n4. 模板class使用检查:');
const templateChecks = [
  { name: '骨架屏class模块化', pattern: /:class="styles\.skeleton/, required: true },
  { name: '容器class模块化', pattern: /:class="styles\.container/, required: true },
  { name: '头部class模块化', pattern: /:class="styles\.header/, required: true },
  { name: '导航class模块化', pattern: /:class="styles\.nav/, required: true },
  { name: '条件class绑定', pattern: /styles\.\w+\s*\?\s*styles\.\w+\s*:\s*styles\.\w+/, required: true },
  { name: '数组class绑定', pattern: /:class="\[[\s\S]*styles\.\w+[\s\S]*\]"/, required: true }
];

templateChecks.forEach(check => {
  const found = check.pattern.test(vueContent);
  const status = found ? '✅' : (check.required ? '❌' : '⚠️');
  console.log(`   ${status} ${check.name}: ${found ? '已实现' : '缺失'}`);
});

// 5. 检查类型声明
console.log('\n5. 类型声明检查:');
const typesContent = results['CSS模块类型声明']?.content || '';
const typesChecks = [
  { name: 'CSS模块类型声明', pattern: /declare module '\*\.module\.css'/, required: true },
  { name: 'Less模块类型声明', pattern: /declare module '\*\.module\.less'/, required: true },
  { name: 'SCSS模块类型声明', pattern: /declare module '\*\.module\.scss'/, required: true },
  { name: '类型导出', pattern: /export default classes/, required: true }
];

typesChecks.forEach(check => {
  const found = check.pattern.test(typesContent);
  const status = found ? '✅' : (check.required ? '❌' : '⚠️');
  console.log(`   ${status} ${check.name}: ${found ? '已实现' : '缺失'}`);
});

// 6. 检查样式隔离效果
console.log('\n6. 样式隔离效果检查:');
const isolationChecks = [
  { name: '局部作用域选择器', pattern: /^\.[a-zA-Z][\w-]*\s*\{/m, required: true },
  { name: '避免全局污染', pattern: /^(?!:global).*\.[a-zA-Z][\w-]*.*\{/m, required: true },
  { name: '保留必要全局样式', pattern: /:global\(page\)/, required: true },
  { name: '动画全局声明', pattern: /:global\(@keyframes/, required: true }
];

isolationChecks.forEach(check => {
  const found = check.pattern.test(cssContent);
  const status = found ? '✅' : (check.required ? '❌' : '⚠️');
  console.log(`   ${status} ${check.name}: ${found ? '已实现' : '缺失'}`);
});

// 7. 统计结果
const allChecks = [...importChecks, ...cssChecks, ...templateChecks, ...typesChecks, ...isolationChecks];
const passedChecks = allChecks.filter(check => {
  if (check.shouldNotExist) {
    if (check.name.includes('全局样式导入')) {
      return !check.pattern.test(vueContent);
    }
  }
  
  let content = '';
  if (check.name.includes('导入') || check.name.includes('模板') || check.name.includes('class')) {
    content = vueContent;
  } else if (check.name.includes('CSS') || check.name.includes('语法') || check.name.includes('隔离')) {
    content = cssContent;
  } else if (check.name.includes('类型')) {
    content = typesContent;
  }
  
  return check.pattern.test(content);
}).length;

const totalChecks = allChecks.length;

console.log('\n📊 CSS模块化结果统计:');
console.log(`   总检查项: ${totalChecks}`);
console.log(`   通过项: ${passedChecks}`);
console.log(`   通过率: ${((passedChecks / totalChecks) * 100).toFixed(1)}%`);

if (passedChecks === totalChecks) {
  console.log('\n🎉 CSS模块化完成！所有检查项都通过了。');
} else if (passedChecks / totalChecks >= 0.8) {
  console.log('\n✅ CSS模块化基本完成，效果良好。');
} else {
  console.log('\n⚠️ CSS模块化还需要进一步完善。');
}

console.log('\n📝 模块化优势:');
console.log('   ✅ 样式隔离: 避免全局样式污染');
console.log('   ✅ 命名冲突: 自动生成唯一class名称');
console.log('   ✅ 可维护性: 更好的代码组织和维护');
console.log('   ✅ 类型安全: TypeScript类型检查支持');
console.log('   ✅ 开发体验: IDE智能提示和错误检查');

console.log('\n🔧 技术特点:');
console.log('   • 使用:class="styles.className"语法');
console.log('   • 支持动态class绑定和条件渲染');
console.log('   • 保留必要的全局样式(:global)');
console.log('   • 完整的TypeScript类型支持');
console.log('   • 与Taro框架完美兼容');

console.log('\n🚀 使用建议:');
console.log('   • 新页面统一使用CSS模块');
console.log('   • 逐步迁移现有页面到CSS模块');
console.log('   • 保持命名规范和代码一致性');
console.log('   • 定期检查和优化样式结构');
