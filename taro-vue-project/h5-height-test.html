<!DOCTYPE html>
<html lang="zh-CN">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>H5高度计算测试</title>
    <style>
        * {
            margin: 0;
            padding: 0;
            box-sizing: border-box;
        }

        body {
            font-family: -apple-system, BlinkMacSystemFont, 'Segoe UI', Roboto, sans-serif;
            background-color: #f8f8f8;
            height: 100vh;
            overflow: hidden;
        }

        .container {
            display: flex;
            flex-direction: column;
            height: 100vh;
        }

        /* 搜索头部样式 */
        .header {
            position: relative;
            display: flex;
            align-items: center;
            width: 100%;
            min-height: 44px; /* 88rpx = 44px */
            background-color: #129799;
            padding: 0 10px; /* 20rpx = 10px */
            box-sizing: border-box;
        }

        .header input {
            flex: 1;
            height: 34px; /* 68rpx = 34px */
            background: #ffffff;
            border-radius: 17px; /* 34rpx = 17px */
            font-size: 14px; /* 28rpx = 14px */
            padding: 0 35px 0 10px; /* 70rpx 0 20rpx = 35px 0 10px */
            border: none;
            outline: none;
        }

        /* Tab切换样式 */
        .country {
            position: relative;
            min-height: 44px; /* 88rpx = 44px */
            border-bottom: 0.5px solid #eee; /* 1rpx = 0.5px */
            background-color: #fff;
            display: flex;
            align-items: center;
            padding: 0 10px;
        }

        .tab-item {
            padding: 8px 16px;
            margin-right: 10px;
            background: #f0f0f0;
            border-radius: 20px;
            font-size: 14px;
            color: #666;
        }

        .tab-item.active {
            background: #8CDAC6;
            color: #fff;
        }

        /* 主内容区域 */
        .main {
            flex: 1;
            height: 1px;
            display: flex;
            background: #fff;
        }

        /* 左侧导航 */
        .nav {
            width: 103px; /* 206rpx = 103px */
            height: 100%;
            background-color: #f6f6f6;
            font-size: 13px; /* 26rpx = 13px */
            color: #0d0d0d;
            overflow-y: auto;
        }

        .nav-item {
            padding-top: 15px; /* 30rpx = 15px */
            box-sizing: border-box;
            display: flex;
            justify-content: center;
        }

        .nav-item:last-child {
            padding-bottom: 15px;
        }

        .nav-name {
            display: flex;
            align-items: center;
            justify-content: center;
            width: 83px; /* 166rpx = 83px */
            height: 32.5px; /* 65rpx = 32.5px */
            padding: 0 9.5px; /* 19rpx = 9.5px */
            box-sizing: border-box;
            background: #ffffff;
            border-radius: 5px; /* 10rpx = 5px */
            margin: auto;
            font-size: 13px; /* 26rpx = 13px */
            border: 2.5px solid #fff; /* 5rpx = 2.5px */
            color: #525252;
            box-shadow: 0px 2px 5.5px -3px rgba(0, 0, 0, 0.46);
            cursor: pointer;
        }

        .nav-name.active {
            border-color: #156f87;
            color: #156f87;
        }

        /* 右侧商品列表 */
        .pro {
            flex: 1;
            height: 100%;
            overflow-y: auto;
            padding: 10px;
        }

        .product-item {
            display: flex;
            padding: 10px;
            margin-bottom: 10px;
            background-color: #fff;
            border-radius: 10px;
            box-shadow: 0 2px 6px 0.5px rgba(3, 3, 3, 0.06);
        }

        .product-image {
            width: 80px;
            height: 80px;
            background: #f0f0f0;
            border-radius: 5px;
            flex-shrink: 0;
        }

        .product-info {
            flex: 1;
            margin-left: 10px;
            display: flex;
            flex-direction: column;
            justify-content: space-between;
        }

        .product-title {
            font-size: 16px;
            color: #333;
            font-weight: bold;
            margin-bottom: 6px;
        }

        .product-subtitle {
            font-size: 14px;
            color: #666;
            margin-bottom: 10px;
        }

        .product-price {
            display: flex;
            align-items: center;
            font-size: 18px;
            color: #e74c3c;
            font-weight: bold;
        }

        /* 调试信息 */
        .debug-info {
            position: fixed;
            top: 10px;
            right: 10px;
            background: rgba(0, 0, 0, 0.8);
            color: white;
            padding: 10px;
            border-radius: 5px;
            font-size: 12px;
            z-index: 1000;
            max-width: 200px;
        }

        .debug-info div {
            margin-bottom: 5px;
        }

        .toggle-btn {
            position: fixed;
            bottom: 20px;
            right: 20px;
            background: #007aff;
            color: white;
            border: none;
            padding: 10px 20px;
            border-radius: 5px;
            cursor: pointer;
            z-index: 1001;
        }
    </style>
</head>
<body>
    <div class="debug-info" id="debugInfo">
        <div>窗口高度: <span id="windowHeight">-</span>px</div>
        <div>头部高度: <span id="headerHeight">-</span>px</div>
        <div>Tab高度: <span id="tabHeight">-</span>px</div>
        <div>主区域高度: <span id="mainHeight">-</span>px</div>
        <div>计算高度: <span id="calculatedHeight">-</span>px</div>
    </div>

    <button class="toggle-btn" onclick="toggleDebug()">切换调试</button>

    <div class="container">
        <!-- 搜索头部 -->
        <div class="header" id="header">
            <input type="text" placeholder="输入你要搜索的内容" disabled>
        </div>

        <!-- Tab切换 -->
        <div class="country" id="country">
            <div class="tab-item active">医美服务</div>
            <div class="tab-item">细胞服务</div>
            <div class="tab-item">韩国品牌</div>
        </div>

        <!-- 主内容区域 -->
        <div class="main" id="main">
            <!-- 左侧导航 -->
            <div class="nav">
                <div class="nav-item">
                    <div class="nav-name active">面部护理</div>
                </div>
                <div class="nav-item">
                    <div class="nav-name">身体护理</div>
                </div>
                <div class="nav-item">
                    <div class="nav-name">皮肤管理</div>
                </div>
                <div class="nav-item">
                    <div class="nav-name">抗衰老</div>
                </div>
                <div class="nav-item">
                    <div class="nav-name">美容整形</div>
                </div>
                <div class="nav-item">
                    <div class="nav-name">牙齿美容</div>
                </div>
                <div class="nav-item">
                    <div class="nav-name">健康检查</div>
                </div>
                <div class="nav-item">
                    <div class="nav-name">其他服务</div>
                </div>
            </div>

            <!-- 右侧商品列表 -->
            <div class="pro">
                <div class="product-item">
                    <div class="product-image"></div>
                    <div class="product-info">
                        <div class="product-title">面部深层清洁护理</div>
                        <div class="product-subtitle">专业面部护理，深层清洁毛孔</div>
                        <div class="product-price">¥299</div>
                    </div>
                </div>
                <div class="product-item">
                    <div class="product-image"></div>
                    <div class="product-info">
                        <div class="product-title">抗衰老精华护理</div>
                        <div class="product-subtitle">高端抗衰老护理，恢复肌肤弹性</div>
                        <div class="product-price">¥599</div>
                    </div>
                </div>
                <div class="product-item">
                    <div class="product-image"></div>
                    <div class="product-info">
                        <div class="product-title">美白补水面膜</div>
                        <div class="product-subtitle">深层补水，提亮肌肤</div>
                        <div class="product-price">¥199</div>
                    </div>
                </div>
                <div class="product-item">
                    <div class="product-image"></div>
                    <div class="product-info">
                        <div class="product-title">眼部护理套餐</div>
                        <div class="product-subtitle">专业眼部护理，减少细纹</div>
                        <div class="product-price">¥399</div>
                    </div>
                </div>
                <div class="product-item">
                    <div class="product-image"></div>
                    <div class="product-info">
                        <div class="product-title">全身SPA护理</div>
                        <div class="product-subtitle">放松身心，舒缓压力</div>
                        <div class="product-price">¥899</div>
                    </div>
                </div>
            </div>
        </div>
    </div>

    <script>
        function calculateHeight() {
            const windowHeight = window.innerHeight;
            const headerEl = document.getElementById('header');
            const tabEl = document.getElementById('country');
            const mainEl = document.getElementById('main');
            
            const headerHeight = headerEl.getBoundingClientRect().height;
            const tabHeight = tabEl.getBoundingClientRect().height;
            const mainHeight = mainEl.getBoundingClientRect().height;
            
            const calculatedHeight = windowHeight - headerHeight - tabHeight;
            
            // 更新调试信息
            document.getElementById('windowHeight').textContent = windowHeight;
            document.getElementById('headerHeight').textContent = headerHeight.toFixed(1);
            document.getElementById('tabHeight').textContent = tabHeight.toFixed(1);
            document.getElementById('mainHeight').textContent = mainHeight.toFixed(1);
            document.getElementById('calculatedHeight').textContent = calculatedHeight.toFixed(1);
            
            console.log('H5高度计算:', {
                windowHeight,
                headerHeight,
                tabHeight,
                mainHeight,
                calculatedHeight
            });
        }

        function toggleDebug() {
            const debugInfo = document.getElementById('debugInfo');
            debugInfo.style.display = debugInfo.style.display === 'none' ? 'block' : 'none';
        }

        // 页面加载完成后计算高度
        window.addEventListener('load', () => {
            setTimeout(calculateHeight, 100);
        });

        // 监听窗口大小变化
        window.addEventListener('resize', () => {
            setTimeout(calculateHeight, 100);
        });

        // 定期更新调试信息
        setInterval(calculateHeight, 1000);
    </script>
</body>
</html>
