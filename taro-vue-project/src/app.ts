import { createApp } from "vue";
import {
  Dialog,
  Input,
  Popup,
  Checkbox,
  Tabs,
  TabPane,
} from "@nutui/nutui-taro";
import { IconFont } from "@nutui/icons-vue-taro";

import "@nutui/nutui-taro/dist/style.css";

import "./app.less";

// 创建应用实例
const App = createApp({
  // 应用启动时的回调函数
  onShow(options) {
    console.log("应用启动，参数：", options);
  },
  // 应用隐藏时的回调函数
  onHide() {
    console.log("应用隐藏");
  },
  // 入口组件不需要实现 render 方法，即使实现了也会被 taro 所覆盖
});

App.use(Dialog)
  .use(Input)
  .use(Popup)
  .use(Checkbox)
  .use(Tabs)
  .use(TabPane)
  .use(IconFont);

export default App;
