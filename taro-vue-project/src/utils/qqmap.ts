// 腾讯地图SDK类型定义
interface QQMapWXInstance {
  reverseGeocoder(options: {
    location: { latitude: number; longitude: number };
    success: (res: any) => void;
    fail: (error: any) => void;
  }): void;
}

// 模拟地图实例
const createMockMapInstance = (): QQMapWXInstance => {
  return {
    reverseGeocoder: (options: any) => {
      // 模拟成功回调
      setTimeout(() => {
        options.success({
          result: {
            address_component: {
              province: "广东省",
              city: "珠海市",
              district: "香洲区",
            },
          },
        });
      }, 100);
    },
  };
};

// 真实地图实例（仅在微信小程序中可用）
const createRealMapInstance = (): QQMapWXInstance | null => {
  try {
    // 在微信小程序环境中，require 是全局可用的
    // 我们通过检查 global 对象来安全地使用 require
    if (typeof global !== "undefined" && (global as any).require) {
      const QQMapWXClass = (global as any).require("./qqmap-wx-jssdk.min.js");
      if (QQMapWXClass) {
        return new QQMapWXClass({
          key: "TFOBZ-4CWKQ-SGZ5Y-2AEO2-5QSYK-YNBCU",
        });
      }
    }
    return null;
  } catch (error) {
    console.error("腾讯地图SDK加载失败:", error);
    return null;
  }
};

// 缓存的地图实例
let mapInstance: QQMapWXInstance | null = null;

/**
 * 获取腾讯地图SDK实例
 * 在微信小程序环境中使用真实SDK，其他环境使用模拟实例
 */
export const getQQMapInstance = (): QQMapWXInstance => {
  if (mapInstance) return mapInstance;
  const realInstance = createRealMapInstance();

  if (realInstance) {
    console.log("腾讯地图SDK加载成功");
    mapInstance = realInstance;
    return mapInstance;
  } else {
    console.warn("腾讯地图SDK加载失败，使用模拟数据");
  }
  // 使用模拟实例
  mapInstance = createMockMapInstance();
  return mapInstance;
};

/**
 * 逆地址解析 - 根据坐标获取地址信息
 */
export const reverseGeocode = (
  latitude: number,
  longitude: number
): Promise<{ State: string; City: string; District: string }> => {
  return new Promise((resolve, reject) => {
    const mapSDK = getQQMapInstance();

    mapSDK.reverseGeocoder({
      location: {
        latitude,
        longitude,
      },
      success: (res) => {
        const address = res.result.address_component;
        console.log("地址解析结果：", address);
        resolve({
          State: address.province,
          City: address.city,
          District: address.district,
        });
      },
      fail: (error) => {
        console.error("逆地址解析失败：", error);
        reject(error);
      },
    });
  });
};
