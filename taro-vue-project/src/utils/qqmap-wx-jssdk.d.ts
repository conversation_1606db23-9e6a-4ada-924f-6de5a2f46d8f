// 腾讯地图微信小程序SDK类型定义
declare module './qqmap-wx-jssdk.min.js' {
  interface QQMapWXOptions {
    key: string;
  }

  interface LocationParam {
    latitude: number;
    longitude: number;
  }

  interface ReverseGeocoderOptions {
    location?: LocationParam;
    coord_type?: number;
    get_poi?: number;
    poi_options?: string;
    success: (res: any) => void;
    fail: (error: any) => void;
    complete?: (res: any) => void;
  }

  interface SearchOptions {
    keyword: string;
    location?: LocationParam;
    region?: string;
    rectangle?: string;
    distance?: string;
    auto_extend?: number;
    orderby?: string;
    page_size?: number;
    page_index?: number;
    address_format?: string;
    filter?: string;
    success: (res: any) => void;
    fail: (error: any) => void;
    complete?: (res: any) => void;
  }

  interface SuggestionOptions {
    keyword: string;
    region?: string;
    region_fix?: number;
    policy?: number;
    page_size?: number;
    page_index?: number;
    get_subpois?: number;
    address_format?: string;
    filter?: string;
    location?: LocationParam;
    success: (res: any) => void;
    fail: (error: any) => void;
    complete?: (res: any) => void;
  }

  interface GeocoderOptions {
    address: string;
    region?: string;
    success: (res: any) => void;
    fail: (error: any) => void;
    complete?: (res: any) => void;
  }

  interface CalculateDistanceOptions {
    to: LocationParam[] | string;
    from?: LocationParam;
    mode?: 'walking' | 'driving' | 'straight';
    success: (res: any) => void;
    fail: (error: any) => void;
    complete?: (res: any) => void;
  }

  interface DirectionOptions {
    to: LocationParam | string;
    from?: LocationParam;
    mode?: 'driving' | 'transit';
    waypoints?: string;
    policy?: number;
    plate_number?: string;
    departure_time?: string;
    success: (res: any) => void;
    fail: (error: any) => void;
    complete?: (res: any) => void;
  }

  class QQMapWX {
    constructor(options: QQMapWXOptions);
    
    /**
     * 地点搜索
     */
    search(options: SearchOptions): void;
    
    /**
     * 关键词输入提示
     */
    getSuggestion(options: SuggestionOptions): void;
    
    /**
     * 逆地址解析（坐标位置描述）
     */
    reverseGeocoder(options: ReverseGeocoderOptions): void;
    
    /**
     * 地址解析（地址转坐标）
     */
    geocoder(options: GeocoderOptions): void;
    
    /**
     * 获取城市列表
     */
    getCityList(options: {
      success: (res: any) => void;
      fail: (error: any) => void;
      complete?: (res: any) => void;
    }): void;
    
    /**
     * 根据城市ID获取区县列表
     */
    getDistrictByCityId(options: {
      id: string;
      success: (res: any) => void;
      fail: (error: any) => void;
      complete?: (res: any) => void;
    }): void;
    
    /**
     * 计算距离
     */
    calculateDistance(options: CalculateDistanceOptions): void;
    
    /**
     * 路线规划
     */
    direction(options: DirectionOptions): void;
  }

  export = QQMapWX;
}
