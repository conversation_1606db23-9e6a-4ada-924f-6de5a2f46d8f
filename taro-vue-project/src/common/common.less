/* 通用样式文件 - 从WePY项目迁移到Taro */

/* 主题色定义 */
@primary-color: #8CDAC6;
@secondary-color: #0f5760;
@success-color: #0fba90;
@warning-color: #ff9500;
@error-color: #ff3333;

/* 通用flex布局混合 */
.flex(@align: center, @justify: space-between) {
  display: flex;
  align-items: @align;
  justify-content: @justify;
}

/* 文本省略号 */
.ellipsis() {
  overflow: hidden;
  text-overflow: ellipsis;
  white-space: nowrap;
}

/* 多行文本省略号 */
.ellipsis-multi(@lines: 2) {
  overflow: hidden;
  text-overflow: ellipsis;
  display: -webkit-box;
  -webkit-line-clamp: @lines;
  -webkit-box-orient: vertical;
}

.btn1-bg {
  background: linear-gradient(to bottom, #1ed5ba, #0d5d51);
  border-radius: 8rpx;
}

.btn2-bg {
  background: linear-gradient(to bottom, #398DB2, #1E4A5E);
  border-radius: 8rpx;
  color: #fff;
}

.btn3-bg {
  background: linear-gradient(to bottom, #A4E7E1, #68D7CD);
  border-radius: 8rpx;
  color: #273d36;
}

/* 清除浮动 */
.clearfix() {
  &::after {
    content: '';
    display: table;
    clear: both;
  }
}

/* 居中定位 */
.center-absolute() {
  position: absolute;
  top: 50%;
  left: 50%;
  transform: translate(-50%, -50%);
}

/* 通用按钮样式 */
.btn-base() {
  display: inline-flex;
  align-items: center;
  justify-content: center;
  padding: 20rpx 40rpx;
  border-radius: 8rpx;
  font-size: 28rpx;
  font-weight: bold;
  border: none;
  cursor: pointer;
  transition: all 0.3s ease;
  
  &:active {
    opacity: 0.8;
  }
  
  &[disabled] {
    opacity: 0.5;
    cursor: not-allowed;
  }
}

/* 主要按钮 */
.btn-primary() {
  .btn-base();
  background: linear-gradient(135deg, @primary-color 0%, @secondary-color 100%);
  color: #fff;
}

/* 次要按钮 */
.btn-secondary() {
  .btn-base();
  background: #fff;
  color: @secondary-color;
  border: 2rpx solid @secondary-color;
}

/* 成功按钮 */
.btn-success() {
  .btn-base();
  background: @success-color;
  color: #fff;
}

/* 卡片样式 */
.card() {
  background: #fff;
  border-radius: 15rpx;
  box-shadow: 0 8rpx 13rpx 0 rgba(3, 48, 60, 0.14);
  overflow: hidden;
}

/* 输入框样式 */
.input-base() {
  width: 100%;
  height: 80rpx;
  padding: 0 20rpx;
  border: 2rpx solid #e0e0e0;
  border-radius: 8rpx;
  font-size: 28rpx;
  background: #fff;
  
  &:focus {
    border-color: @primary-color;
    outline: none;
  }
  
  &::placeholder {
    color: #999;
  }
}

/* 页面容器 */
.page-container() {
  min-height: 100vh;
  background: #f6f6f6;
}

/* 内容容器 */
.content-container() {
  padding: 20rpx;
}

/* 标题样式 */
.title-large() {
  font-size: 36rpx;
  font-weight: bold;
  color: @secondary-color;
}

.title-medium() {
  font-size: 32rpx;
  font-weight: bold;
  color: @secondary-color;
}

.title-small() {
  font-size: 28rpx;
  font-weight: bold;
  color: @secondary-color;
}

/* 文本样式 */
.text-primary() {
  color: @secondary-color;
}

.text-secondary() {
  color: #666;
}

.text-muted() {
  color: #999;
}

.text-success() {
  color: @success-color;
}

.text-warning() {
  color: @warning-color;
}

.text-error() {
  color: @error-color;
}

/* 间距 */
.margin-top(@size: 20rpx) {
  margin-top: @size;
}

.margin-bottom(@size: 20rpx) {
  margin-bottom: @size;
}

.padding(@size: 20rpx) {
  padding: @size;
}

/* 阴影 */
.shadow-light() {
  box-shadow: 0 2rpx 8rpx rgba(0, 0, 0, 0.1);
}

.shadow-medium() {
  box-shadow: 0 4rpx 12rpx rgba(0, 0, 0, 0.15);
}

.shadow-heavy() {
  box-shadow: 0 8rpx 24rpx rgba(0, 0, 0, 0.2);
}

/* 动画 */
.fade-in() {
  animation: fadeIn 0.3s ease-in-out;
}

@keyframes fadeIn {
  from {
    opacity: 0;
  }
  to {
    opacity: 1;
  }
}

.slide-up() {
  animation: slideUp 0.3s ease-out;
}

@keyframes slideUp {
  from {
    transform: translateY(100%);
  }
  to {
    transform: translateY(0);
  }
}

@keyframes loading {
  0% {
    background-position: 200% 0;
  }
  100% {
    background-position: -200% 0;
  }
}

/* 响应式断点 */
@mobile: ~'(max-width: 767px)';
@tablet: ~'(min-width: 768px) and (max-width: 1023px)';
@desktop: ~'(min-width: 1024px)';
