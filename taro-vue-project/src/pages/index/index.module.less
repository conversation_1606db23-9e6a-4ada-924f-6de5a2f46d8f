/* 首页样式模块 - CSS Modules */
@import '../../common/common.less';

/* 页面基础样式 */
:global(page) {
  background-color: #f6f6f6;
}

.container {
  background-color: #f6f6f6;
  min-height: 100vh;
}

/* 骨架屏样式 */
.skeleton {
  .skeletonHeader {
    height: 200rpx;
    background: linear-gradient(90deg, #f0f0f0 25%, #e0e0e0 50%, #f0f0f0 75%);
    background-size: 200% 100%;
    animation: loading 1.5s infinite;
  }

  .skeletonBanner {
    height: 280rpx;
    margin: 28rpx 20rpx;
    background: linear-gradient(90deg, #f0f0f0 25%, #e0e0e0 50%, #f0f0f0 75%);
    background-size: 200% 100%;
    animation: loading 1.5s infinite;
    border-radius: 8rpx;
  }

  .skeletonMenu {
    height: 300rpx;
    margin: 20rpx;
    background: linear-gradient(90deg, #f0f0f0 25%, #e0e0e0 50%, #f0f0f0 75%);
    background-size: 200% 100%;
    animation: loading 1.5s infinite;
    border-radius: 20rpx;
  }

  .skeletonContent {
    height: 600rpx;
    margin: 20rpx;
    background: linear-gradient(90deg, #f0f0f0 25%, #e0e0e0 50%, #f0f0f0 75%);
    background-size: 200% 100%;
    animation: loading 1.5s infinite;
    border-radius: 15rpx;
  }
}

/* 按钮样式 - 从原项目迁移 */
.btn1Bg {
  padding: 10rpx 20rpx;
  border-radius: 20rpx;
  background: linear-gradient(to bottom, #baf8e5, #8ef4d4);
  border: none;
  text {
    font-size: 18rpx;
    color: #050505;
    font-weight: bold;
  }
}

.btn2Bg {
  padding: 10rpx 20rpx;
  border-radius: 20rpx;
  background: linear-gradient(to bottom, #e6e6e6, #d1d1d1);
  border: none;
  text {
    font-size: 18rpx;
    color: #073a38;
    font-weight: bold;
  }
}

.btn3Bg {
  padding: 10rpx 20rpx;
  border-radius: 20rpx;
  background: linear-gradient(to bottom, #36aaba, #30626a);
  border: none;
  margin: 0;
  text {
    font-size: 18rpx;
    color: #fff;
    font-weight: bold;
  }
}

/* 输入框样式 */
.nameInput {
  margin: 20rpx 0;
  padding: 20rpx;
  border: 1rpx solid #e5e5e5;
  border-radius: 8rpx;
  background-color: #fff;
}

/* 红包样式 - 从原项目迁移 */
.redpacket {
  position: fixed;
  right: 35rpx;
  bottom: 100rpx;
  z-index: 9;
  width: 295rpx;
  height: 220rpx;
  
  .line {
    position: absolute;
    left: 144rpx;
    top: -550rpx;
    width: 5rpx;
    height: 550rpx;
    background-color: #ce0211;
  }
  
  .con {
    position: relative;
    width: 100%;
    height: 100%;
    
    .box {
      width: 100%;
      height: 100%;
    }
    
    .close {
      position: absolute;
      right: -25rpx;
      top: -25rpx;
      width: 60rpx;
      height: 60rpx;
      display: flex;
      align-items: center;
      justify-content: center;
      
      image {
        width: 100%;
        height: 100%;
        width: 42rpx;
        height: 42rpx;
      }
    }
  }
}

.redpacketSwiper {
  width: 750rpx;
  
  .con {
    width: 100%;
    height: 450rpx;
    
    .item {
      position: relative;
      width: 100%;
      height: 375rpx;
      
      image {
        position: absolute;
        left: 50%;
        top: 0;
        margin-left: -285rpx;
        width: 570rpx;
        height: 100%;
      }
      
      .price {
        position: absolute;
        left: 0%;
        top: 150rpx;
        width: 100%;
        text-align: center;
        color: #ffe77a;
        font-weight: 900;
        z-index: 99;
        font-size: 80rpx;
        
        .unit {
          font-size: 50rpx;
        }
      }
    }
  }
  
  .close {
    display: block;
    width: 60rpx;
    height: 60rpx;
    margin: 55rpx auto 0;
  }
  
  :global(.wx-swiper-dot.wx-swiper-dot-active) {
    width: 30rpx;
    border-radius: 15rpx;
  }
}

/* 自定义头部样式 - 从原项目迁移 */
.customHeader {
  position: sticky;
  top: 0;
  background-color: @primary-color;
  z-index: 10;
  
  .headerTitle {
    position: relative;
    display: flex;
    align-items: center;
    justify-content: center;
    height: 88rpx;
    
    image {
      position: absolute;
      left: 55rpx;
      top: 0;
      width: 90rpx;
      height: 90rpx;
    }
    
    .shopName {
      text-align: center;
      
      .name1 {
        font-size: 25rpx;
        color: #0e5453;
        font-weight: bold;
      }
      
      .name2 {
        font-size: 20rpx;
        color: #0e5453;
      }
    }
  }
  
  .header {
    .flex();
    padding: 0 24rpx;
    width: 100%;
    height: 88rpx;
    box-sizing: border-box;
    z-index: 10;
    
    .left {
      .flex(center,center);
      
      .icon1 {
        width: 35rpx;
        height: 35rpx;
      }
      
      view {
        font-size: 30rpx;
        padding: 0 8rpx;
        color: #0f5760;
      }
      
      .icon2 {
        width: 0;
        height: 0;
        border-left: 10rpx solid transparent;
        border-right: 10rpx solid transparent;
        border-top: 20rpx solid #0e5453;
        margin-left: 8rpx;
      }
    }
    
    .mid {
      .flex(center, space-between);
      flex: 1;
      height: 53rpx;
      background: #ffffff;
      border-radius: 24rpx;
      margin: 0 10rpx;
      padding: 0 10rpx;
      
      .icon {
        font-size: 34rpx;
        color: #0e5453;
      }
      
      text {
        font-size: 25rpx;
        color: #b3b3b3;
      }
    }
    
    .right {
      .flex(center, center);
      color: #0f5760;
      font-size: 28rpx;
      
      image {
        width: 30rpx;
        height: 30rpx;
      }
      
      view {
        line-height: 1.2;
        
        .icon {
          width: 0;
          height: 0;
          border-top: 10rpx solid transparent;
          border-bottom: 10rpx solid transparent;
          border-right: 20rpx solid #0e5453;
          margin-left: 8rpx;
        }
      }
    }
  }
}

/* 主要内容区域 */
.main {
  padding: 0 20rpx;

  .swiper {
    margin-top: 28rpx;
    height: 280rpx;

    .swiperItem {
      width: 100%;
      height: 100%;
      border-radius: 8rpx;
    }
  }

  .tips {
    margin: 30rpx 0;
    background-color: #fff;
    border-radius: 6rpx;
    font-size: 32rpx;
    color: #1c1c1d;
    padding: 9rpx 20rpx;
    line-height: 48rpx;
    text-align: center;
    .ellipsis();
  }

  .tabs {
    display: flex;
    align-items: center;
    flex-wrap: wrap;
    margin: 20rpx 0;
    background-color: #fff;
    padding: 40rpx 40rpx 0;
    box-shadow: 0 8rpx 13rpx 0 rgba(3, 48, 60, 0.14);
    border-radius: 20rpx;

    .tabItem {
      margin-bottom: 40rpx;

      &:not(:nth-child(4n)) {
        margin-right: 34rpx;
      }

      image {
        width: 130rpx;
        height: 125rpx;
      }

      view {
        width: 130rpx;
        font-size: 28rpx;
        color: #5b5a5a;
        text-align: center;
        .ellipsis();
      }
    }
  }

  .box {
    margin-top: 20rpx;

    .head {
      .flex(center,center);
      height: 66rpx;
      background: #c6fced;
      box-shadow: 0rpx 8rpx 13rpx 0rpx rgba(3, 48, 60, 0.14);
      border-radius: 15rpx;

      image {
        width: 150rpx;
        height: 35rpx;
      }

      text {
        font-size: 34rpx;
        color: #001417;
        font-weight: bold;
        font-style: italic;
      }
    }

    .ptHead {
      background: #3b6373;
      box-shadow: 0rpx 8rpx 13rpx 0rpx rgba(3, 48, 60, 0.14);
      border-radius: 15rpx;

      text {
        font-size: 34rpx;
        color: #fff;
        font-weight: bold;
        font-style: italic;
      }
    }

    .yrHead {
      background: #67cbc0;
      box-shadow: 0rpx 8rpx 13rpx 0rpx rgba(3, 48, 60, 0.14);
      border-radius: 15rpx;

      text {
        font-size: 34rpx;
        color: #001417;
        font-weight: bold;
        font-style: italic;
      }
    }

    .con {
      .flex();
      flex-wrap: wrap;
      margin-top: 20rpx;

      .item {
        width: 343rpx;
        box-sizing: border-box;
        border-radius: 15rpx;
        background-color: #fff;
        padding: 27rpx 23rpx;
        margin-bottom: 20rpx;
        box-shadow: 0 8rpx 13rpx 0 rgba(3, 48, 60, 0.14);

        .time {
          .flex(center,start);
          font-size: 17rpx;
          height: 38rpx;
          border-radius: 17rpx;
          color: #0d545d;
          background-color: #c6fced;

          .key {
            margin-left: 18rpx;
          }

          .countdown {
            font-size: 17rpx !important;
            color: #0d545d !important;
            font-weight: bold;
            line-height: 1 !important;
          }

          &.ms {
            background-color: #3b6373;
            border-radius: 0;
            color: #fff;

            .countdown {
              color: #fff !important;
            }
          }
        }

        .img {
          position: relative;
          width: 298rpx;
          height: 298rpx;
          margin-top: 8rpx;

          image {
            width: 100%;
            height: 100%;
          }

          .cover {
            width: 100%;
            height: 100%;
            background-color: rgba(0, 0, 0, 0.4);
            position: absolute;
            left: 0;
            top: 0;
            .flex(center,center);

            image {
              width: 260rpx;
              height: 177rpx;
            }
          }
        }

        .num {
          .flex(center,start);
          font-size: 17rpx;
          height: 38rpx;
          border-radius: 17rpx;
          color: #0d545d;
          background-color: #c6fced;
          margin-top: 8rpx;

          .xsqg {
            margin-left: 18rpx;
            font-size: 17rpx !important;
            color: #0d545d !important;
            font-weight: bold;
          }

          &.ms {
            background-color: #3b6373;
            border-radius: 0;
            color: #fff;

            image {
              width: 30rpx;
              height: 30rpx;
              margin-left: 18rpx;
              margin-right: 8rpx;
            }
          }
        }

        .price {
          .flex(center, space-between);
          margin-top: 8rpx;

          .p1 {
            font-size: 32rpx;
            color: #e74c3c;
            font-weight: bold;
          }

          .p2 {
            font-size: 24rpx;
            color: #999;
            text-decoration: line-through;
          }

          &.ms {
            .left {
              .p1 {
                font-size: 32rpx;
                color: #e74c3c;
                font-weight: bold;
              }

              .p2 {
                font-size: 24rpx;
                color: #999;
                text-decoration: line-through;
              }
            }

            .right {
              .flex(center, center);
              font-size: 20rpx;
              color: #666;

              image {
                width: 24rpx;
                height: 24rpx;
                margin-left: 4rpx;
              }
            }
          }
        }

        .info {
          margin-top: 8rpx;

          .name {
            font-size: 28rpx;
            color: #333;
            font-weight: bold;
            .ellipsis();
          }

          .des {
            font-size: 24rpx;
            color: #666;
            margin-top: 4rpx;
            .ellipsis();
          }
        }

        .btns {
          .flex(center, space-between);
          margin-top: 12rpx;

          .left {
            flex: 1;
            height: 8rpx;
            background-color: #f0f0f0;
            border-radius: 4rpx;
            margin-right: 12rpx;
            position: relative;

            .bar {
              height: 100%;
              background: linear-gradient(to right, #8ef4d4, #baf8e5);
              border-radius: 4rpx;
            }
          }

          .right {
            .flex(center, center);

            button {
              margin-left: 8rpx;
            }
          }

          &.ms {
            .right {
              button {
                margin-left: 8rpx;
              }
            }
          }

          &.yr {
            .right {
              button {
                .flex(center, center);
                padding: 8rpx 16rpx;
                border-radius: 20rpx;
                border: none;
                margin-left: 8rpx;
                background: linear-gradient(to bottom, #baf8e5, #8ef4d4);

                image {
                  width: 24rpx;
                  height: 24rpx;
                  margin-right: 4rpx;
                }

                text {
                  font-size: 18rpx;
                  color: #050505;
                  font-weight: bold;
                }
              }
            }
          }
        }
      }
    }

    .more {
      .flex(center, center);
      height: 60rpx;
      background-color: #fff;
      margin-top: 20rpx;
      border-radius: 15rpx;
      font-size: 28rpx;
      color: #666;
      box-shadow: 0 8rpx 13rpx 0 rgba(3, 48, 60, 0.14);
    }
  }

  .list {
    margin-top: 20rpx;

    .head {
      .flex(center, space-between);
      height: 66rpx;
      background: #c6fced;
      box-shadow: 0rpx 8rpx 13rpx 0rpx rgba(3, 48, 60, 0.14);
      border-radius: 15rpx;
      padding: 0 20rpx;
      font-size: 34rpx;
      color: #001417;
      font-weight: bold;
      font-style: italic;

      text {
        font-size: 24rpx;
        color: #666;
        font-weight: normal;
        font-style: normal;
      }
    }

    .con {
      .flex();
      flex-wrap: wrap;
      margin-top: 20rpx;

      .listItem {
        width: 343rpx;
        box-sizing: border-box;
        border-radius: 15rpx;
        background-color: #fff;
        padding: 27rpx 23rpx;
        margin-bottom: 20rpx;
        box-shadow: 0 8rpx 13rpx 0 rgba(3, 48, 60, 0.14);
        .flex();
        align-items: center;

        &:nth-child(odd) {
          margin-right: 20rpx;
        }

        .name {
          flex: 1;
          font-size: 28rpx;
          color: #333;
          font-weight: bold;
          .ellipsis();
        }

        image {
          width: 120rpx;
          height: 120rpx;
          border-radius: 8rpx;
          margin-left: 12rpx;
        }
      }
    }

    .more {
      .flex(center, center);
      height: 60rpx;
      background-color: #fff;
      margin-top: 20rpx;
      border-radius: 15rpx;
      font-size: 28rpx;
      color: #666;
      box-shadow: 0 8rpx 13rpx 0 rgba(3, 48, 60, 0.14);
    }
  }
}

/* 弹窗样式 */
.newsBody {
  padding: 20rpx;
  font-size: 28rpx;
  line-height: 1.6;
  color: #333;
}

.modalContent {
  padding: 20rpx;
}

/* 回到顶部按钮 */
.top {
  position: fixed;
  right: 30rpx;
  bottom: 200rpx;
  width: 80rpx;
  height: 80rpx;
  z-index: 999;
}
