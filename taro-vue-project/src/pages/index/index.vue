<template>
  <!-- 骨架屏 -->
  <view v-if="initLoading" :class="styles.skeleton">
    <view :class="styles.skeletonHeader"></view>
    <view :class="styles.skeletonBanner"></view>
    <view :class="styles.skeletonMenu"></view>
    <view :class="styles.skeletonContent"></view>
  </view>

  <view v-else :class="styles.container">
    <!-- 红包浮动图标 -->
    <view :class="styles.redpacket" v-if="repacketIconShow && redpacketData.length > 0">
      <view :class="styles.con">
        <image
          src="https://pub-catering.utcook.com/FqImy6UJWG5lnr1YFYb-aJishWev"
          :class="styles.box"
          @tap="onOpenRedpacket"
        />
        <view :class="styles.close" @tap="repacketIconShow = false">
          <image :src="closeIcon" />
        </view>
      </view>
    </view>

    <!-- 红包弹窗 -->
    <nut-dialog v-model="redpacketShow" @close="onCloseRedpacket">
      <view :class="styles.redpacketSwiper">
        <swiper
          :class="styles.con"
          :indicator-dots="true"
          indicator-color="#ccc"
          indicator-active-color="#8CDAC6"
        >
          <swiper-item v-for="(item, i) in redpacketData" :key="i">
            <view :class="styles.item">
              <view :class="styles.price"
                ><span :class="styles.unit">¥</span>{{ item.Bonus }}</view
              >
              <image
                src="https://pub-catering.utcook.com/Ftz4TiCuC14DmiXP5hmHwISPDsGa"
              />
            </view>
          </swiper-item>
        </swiper>
        <image :src="close2Icon" :class="styles.close" @tap="onCloseRedpacket" />
      </view>
    </nut-dialog>

    <!-- 自定义头部 -->
    <view :class="styles.customHeader">
      <view
        :class="styles.statusBar"
        :style="{ height: statusBarHeight + 'px' }"
      ></view>
      <view :class="styles.headerTitle">
        <image
          src="https://cos.annmun1.net/62Ij3DCU8e58da639bbcd5e4ad053ee4cd1f3bf86744.png"
        />
        <view :class="styles.shopName" @tap="openEditShopName">
          <view :class="styles.name1">{{ shopName || "琳歌医疗大健康机构" }}</view>
          <view :class="styles.name2">医美, 细胞服务, 医疗大健康服务平台</view>
        </view>
      </view>
      <view
        :class="styles.header"
        :style="{ top: statusBarHeight + titleHeight + 'px' }"
      >
        <view :class="styles.mid" @tap="handleSearchClick">
          <text>请输入想要的项目或产品</text>
          <text :class="styles.icon">🔍</text>
        </view>
        <view v-if="!isLogin" :class="styles.right" @tap="handleLoginClick">
          <view>
            免费注册商城
            <view style="display: flex; align-items: center">
              登录Login<span :class="styles.icon"></span>
            </view>
          </view>
        </view>
      </view>
    </view>

    <!-- 主要内容区域 -->
    <view :class="styles.main">
      <!-- 轮播图 -->
      <swiper
        :class="styles.swiper"
        indicator-color="#A1A1A1"
        indicator-active-color="#fff"
        :indicator-dots="true"
        :autoplay="autoplay"
        @change="swiperChange"
      >
        <swiper-item v-for="(item, i) in banner" :key="i">
          <video
            v-if="item.VidUrl"
            :src="item.VidUrl"
            :poster="item.imgUrl"
            :id="i"
            @play="onPlay"
            @pause="onPauseAndEnd"
            @ended="onPauseAndEnd"
            :class="styles.swiperItem"
          ></video>
          <image
            v-else
            :src="item.imgUrl"
            :class="styles.swiperItem"
            mode="aspectFill"
          />
        </swiper-item>
      </swiper>

      <!-- 提示信息 -->
      <view
        :class="styles.tips"
        :style="{
          fontSize: news.Title && news.Title.length > 22 ? '28rpx' : '32rpx',
        }"
        @tap="show = true"
        v-if="news.Title"
      >
        {{ news.Title }}
      </view>

      <!-- 功能菜单 -->
      <view :class="styles.tabs">
        <view
          v-for="(item, i) in menu.slice(0, 12)"
          :key="i"
          :class="styles.tabItem"
          @tap="onTab(item)"
        >
          <image :src="item.imgUrl" mode="aspectFill" />
          <view>{{ item.HomeMenuDescrip }}</view>
        </view>
      </view>

      <!-- 限时抢购 -->
      <view :class="styles.box" v-if="!initLoading && limitTimePro.length">
        <view :class="styles.head">
          <text>限时抢购</text>
        </view>
        <view :class="styles.con">
          <view
            :class="styles.item"
            v-for="(item, i) in limitTimePro"
            :key="i"
            @tap="handleProductClick(item, false)"
          >
            <view :class="styles.time">
              <text :class="styles.key">距离结束：</text>
              <text :class="styles.countdown">{{
                formatCountDown(item.MilliSeconds)
              }}</text>
            </view>
            <view :class="styles.img">
              <image :src="item.Image" />
              <view :class="styles.cover" v-if="item.LeftQty === 0">
                <image :src="soldOutImg" />
              </view>
            </view>
            <view :class="styles.num">
              <text :class="styles.xsqg">限时抢购</text>
              <text>已抢{{ item.SalesQty }}件, 仅剩 {{ item.LeftQty }} 件</text>
            </view>
            <view :class="styles.price">
              <view :class="styles.p1">
                {{ item.PointProduct === "True" ? "" : "￥"
                }}{{ item.MemberPrice
                }}{{ item.PointProduct === "True" ? "积分" : "" }}
              </view>
              <view :class="styles.p2">
                立省{{ item.PointProduct === "True" ? "" : "￥"
                }}{{ item.CurrentSave }}
              </view>
            </view>
            <view :class="styles.info">
              <view :class="styles.name">{{ item.Title }}</view>
              <view :class="styles.des">{{ item.SubTitle || item.Title }}</view>
            </view>
            <view :class="styles.btns">
              <view :class="styles.left">
                <view :class="styles.bar" :style="{ width: item.percent + '%' }"></view>
              </view>
              <view :class="styles.right">
                <button :class="styles.btn1Bg" v-if="item.LeftQty > 0">
                  <text>立即抢购</text>
                </button>
                <button :class="styles.btn2Bg" v-else>
                  <text>已抢光</text>
                </button>
              </view>
            </view>
          </view>
        </view>
        <view :class="styles.more" @tap="handleMoreClick('limitTime')">点击更多</view>
      </view>

      <!-- 拼团秒杀 -->
      <view :class="styles.box" v-if="!initLoading && groupPro.length">
        <view :class="[styles.head, styles.ptHead]">
          <text>拼团秒杀</text>
        </view>
        <view :class="styles.con">
          <view
            :class="styles.item"
            v-for="(item, i) in groupPro"
            :key="i"
            @tap="handleProductClick(item, true, item.GroupMembers)"
          >
            <view :class="[styles.time, styles.ms]">
              <text :class="styles.key">距离结束：</text>
              <text :class="styles.countdown">{{
                formatCountDown(item.MilliSeconds)
              }}</text>
            </view>
            <view :class="styles.img">
              <image :src="item.Image" />
              <view :class="styles.cover" v-if="item.LeftQty === 0">
                <image :src="soldOutImg" />
              </view>
            </view>
            <view :class="[styles.num, styles.ms]">
              <image :src="msIcon" />
              <text>已抢{{ item.SalesQty }}件, 仅剩 {{ item.LeftQty }} 件</text>
            </view>
            <view :class="[styles.price, styles.ms]">
              <view :class="styles.left">
                <view :class="styles.p1">
                  {{ item.PointProduct === "True" ? "" : "￥"
                  }}{{ item.MemberPrice
                  }}{{ item.PointProduct === "True" ? "积分" : "" }}
                </view>
                <view :class="styles.p2">立省￥{{ item.CurrentSave }}</view>
              </view>
              <view :class="styles.right">
                <text>{{ item.GroupMembers }}人团</text>
                <image :src="xiaoren" />
                <image :src="xiaoren" />
              </view>
            </view>
            <view :class="styles.info">
              <view :class="styles.name">{{ item.Title }}</view>
              <view :class="styles.des">{{ item.SubTitle }}</view>
            </view>
            <view :class="[styles.btns, styles.ms]">
              <view :class="styles.left">
                <view :class="styles.bar" :style="{ width: item.percent + '%' }"></view>
              </view>
              <view :class="styles.right">
                <button :class="styles.btn2Bg" v-if="item.LeftQty > 0">
                  <text>发起拼团</text>
                </button>
                <button :class="styles.btn3Bg" v-if="item.LeftQty > 0">
                  <text>立即参团</text>
                </button>
                <button :class="styles.btn2Bg" v-if="item.LeftQty === 0">
                  <text>已抢光</text>
                </button>
              </view>
            </view>
          </view>
        </view>
        <view :class="styles.more" @tap="handleMoreClick('groupBuy')">点击更多</view>
      </view>

      <!-- 预热新品 -->
      <view
        :class="styles.box"
        v-if="!initLoading && (preLimitTimePro.length || preGroupPro.length)"
      >
        <view :class="[styles.head, styles.yrHead]">
          <text>预热新品</text>
        </view>
        <!-- 预热限时抢购 -->
        <view :class="styles.con" v-if="preLimitTimePro.length">
          <view
            :class="styles.item"
            v-for="(item, i) in preLimitTimePro"
            :key="i"
            @tap="handleProductClick(item, false, null, 'pre', 'LT')"
          >
            <view :class="styles.time">
              <text :class="styles.key">距离开始：</text>
              <text :class="styles.countdown">{{
                formatCountDown(item.MilliSeconds)
              }}</text>
            </view>
            <view :class="styles.img">
              <image :src="item.Image" />
            </view>
            <view :class="styles.num">
              <text :class="styles.xsqg">限时抢购</text>
              <text>暂未开始</text>
            </view>
            <view :class="styles.price">
              <view :class="styles.p1">
                {{ item.PointProduct === "True" ? "" : "￥"
                }}{{ item.MemberPrice
                }}{{ item.PointProduct === "True" ? "积分" : "" }}
              </view>
              <view :class="styles.p2">
                立省{{ item.PointProduct === "True" ? "" : "￥"
                }}{{ item.CurrentSave }}
              </view>
            </view>
            <view :class="styles.info">
              <view :class="styles.name">{{ item.Title }}</view>
              <view :class="styles.des">{{ item.SubTitle }}</view>
            </view>
            <view :class="[styles.btns, styles.yr]">
              <view :class="styles.left">
                <view :class="styles.bar" :style="{ width: item.percent + '%' }"></view>
              </view>
              <view :class="styles.right">
                <button @tap.stop="handlePreOrder(item, 'limit')">
                  <image :src="ljctBtn" />
                  <text>立即抢购</text>
                </button>
              </view>
            </view>
          </view>
        </view>
        <view
          v-if="preLimitTimePro.length > 0"
          :class="styles.more"
          @tap="handleMoreClick('preLimitTime')"
        >
          点击更多
        </view>
        <!-- 预热拼团 -->
        <view :class="styles.con" v-if="preGroupPro.length">
          <view
            :class="styles.item"
            v-for="(item, i) in preGroupPro"
            :key="i"
            @tap="
              handleProductClick(item, true, item.GroupMembers, 'pre', 'GP')
            "
          >
            <view :class="[styles.time, styles.ms]">
              <text :class="styles.key">距离开始：</text>
              <text :class="styles.countdown">{{
                formatCountDown(item.MilliSeconds)
              }}</text>
            </view>
            <view :class="styles.img">
              <image :src="item.Image" />
            </view>
            <view :class="[styles.num, styles.ms]">
              <image :src="msIcon" />
              <text>暂未开始</text>
            </view>
            <view :class="[styles.price, styles.ms]">
              <view :class="styles.left">
                <view :class="styles.p1">
                  {{ item.PointProduct === "True" ? "" : "￥"
                  }}{{ item.MemberPrice
                  }}{{ item.PointProduct === "True" ? "积分" : "" }}
                </view>
                <view :class="styles.p2">
                  立省{{ item.PointProduct === "True" ? "" : "￥"
                  }}{{ item.CurrentSave }}
                </view>
              </view>
              <view :class="styles.right">
                <text>{{ item.GroupMembers }}人团</text>
                <image :src="xiaoren" />
                <image :src="xiaoren" />
              </view>
            </view>
            <view :class="styles.info">
              <view :class="styles.name">{{ item.Title }}</view>
              <view :class="styles.des">{{ item.SubTitle }}</view>
            </view>
            <view :class="[styles.btns, styles.yr]">
              <view :class="styles.left">
                <view :class="styles.bar" :style="{ width: item.percent + '%' }"></view>
              </view>
              <view :class="styles.right">
                <button @tap.stop="handlePreOrder(item, 'group')">
                  <image :src="ljctBtn" />
                  <text>发起拼团</text>
                </button>
                <button @tap.stop="handlePreOrder(item, 'join')">
                  <image :src="fqptBtn" />
                  <text>立即参团</text>
                </button>
              </view>
            </view>
          </view>
        </view>
        <view
          v-if="preGroupPro.length > 0"
          :class="styles.more"
          @tap="handleMoreClick('preGroupBuy')"
        >
          点击更多
        </view>
      </view>

      <!-- 服务分类列表 -->
      <view :class="styles.list" v-if="!initLoading">
        <view v-for="(item, i) in serviceList" :key="item.MainCategoryCode">
          <view v-if="item.category">
            <view :class="styles.head">
              {{ item.MainCategoryDescrip }}
            </view>
            <view :class="styles.con">
              <view
                v-for="item2 in getDisplayCategories(item)"
                :key="item2.CategoryCode"
                :class="styles.listItem"
                @tap="
                  onGoClass(item2.CategoryCode, item2.CategoryDescrip, true)
                "
              >
                <view :class="styles.name">{{ item2.CategoryDescrip }}</view>
                <image :src="item2.Image" mode="aspectFill" />
              </view>
            </view>
            <view
              v-if="item.category.length > 4"
              :class="styles.more"
              @tap="onViewAllService(i)"
            >
              {{ item.viewAll ? "收起" : "点击更多" }}
            </view>
          </view>
        </view>
      </view>

      <!-- 商品分类列表 -->
      <view :class="styles.list" v-if="!initLoading">
        <view v-for="(item, i) in category" :key="item.CountryBrandCode">
          <view v-if="item.category">
            <view :class="styles.head">
              {{ item.CountryBrandDescrip }}
              <text>{{
                item.CountryBrandDescrip === "中国品牌"
                  ? "特选新品"
                  : "品牌产品"
              }}</text>
            </view>
            <view :class="styles.con">
              <view
                v-for="item2 in getDisplayCategories(item)"
                :key="item2.CategoryCode"
                :class="styles.listItem"
                @tap="onGoClass(item2.CategoryCode, item2.CategoryDescrip)"
              >
                <view :class="styles.name">{{ item2.CategoryDescrip }}</view>
                <image :src="item2.Image" mode="aspectFill" />
              </view>
            </view>
            <view
              v-if="item.category.length > 4"
              :class="styles.more"
              @tap="onViewAllCategory(i)"
            >
              {{ item.viewAll ? "收起" : "点击更多" }}
            </view>
          </view>
        </view>
      </view>
    </view>

    <!-- 公告弹窗 -->
    <nut-dialog v-model="show" @cancel="onClose" title="公告">
      <view :class="styles.newsBody">
        <rich-text :nodes="news.Title" />
      </view>
    </nut-dialog>

    <!-- 修改店铺名称弹窗 -->
    <nut-dialog
      v-model:visible="showEditShopName"
      @cancel="onCancelShopName"
      @ok="onEditShopName"
      title="修改名称"
      ok-text="确定"
      cancel-text="取消"
    >
      <view :class="styles.modalContent">
        <nut-input
          v-model="newShopName"
          placeholder="请输入名称"
          :class="styles.nameInput"
        />
      </view>
    </nut-dialog>

    <!-- 回到顶部按钮 -->
    <image v-if="floorstatus" :src="topIcon" :class="styles.top" @tap="goTop" />
  </view>
</template>

<script lang="ts" setup>
import { ref, onMounted, onUnmounted, computed } from "vue";
import Taro, {
  useDidShow,
  usePageScroll,
  usePullDownRefresh,
} from "@tarojs/taro";
// 引入API服务 - 从原WePY项目迁移
import {
  homeBanner,
  menuList,
  NewsListing,
  LimitedTimeProduct,
  GroupPurchaseProduct,
  PreLimitedTimeProduct,
  PreGroupPurchaseProduct,
  HomePageCountryBrand,
  ServiceHomePageMainCategory,
  Listing_AngpaoBonusDetail,
  CheckAngPao,
  CheckLogin,
  UpdateMemberShopName,
} from "../../services/api";
import styles from "./index.module.less";

// 静态资源路径定义
const closeIcon = "../../static/home/<USER>/close.png";
const close2Icon = "../../static/home/<USER>/close2.png";
const soldOutImg = "../../static/home/<USER>";
const msIcon = "../../static/home/<USER>";
const xiaoren = "../../static/home/<USER>";
const ljctBtn = "../../static/home/<USER>";
const fqptBtn = "../../static/home/<USER>";
const topIcon = "../../static/home/<USER>";

// 响应式数据定义 - 从WePY项目迁移
const statusBarHeight = ref(0); // 状态栏高度
const titleHeight = ref(0); // 标题高度
const floorstatus = ref(false);
const initLoading = ref(true);
const autoplay = ref(true);
const show = ref(false);
const repacketIconShow = ref(true); // 小红包图标显示
const redpacketShow = ref(false); // 红包详情轮播
const redpacketData = ref([]); // 红包列表
const news = ref({}); // 公告
const banner = ref([]);
const menu = ref([]);
const limitTimePro = ref([]); // 限时抢购列表
const groupPro = ref([]); // 拼团列表
const preLimitTimePro = ref([]); // 预热限时抢购列表
const preGroupPro = ref([]); // 预热拼团列表
const category = ref([]); // 国家商品分类
const serviceList = ref([]); // 服务主分类列表
const isLogin = ref(false);
const shopName = ref(""); // 店铺名称
const newShopName = ref(""); // 新店铺名称
const showEditShopName = ref(false); // 是否显示修改店铺名称

// 倒计时相关
const countdownTimers = ref(new Map()); // 存储所有倒计时定时器

// 计算属性 - 用于处理分类显示逻辑
const getDisplayCategories = computed(() => {
  return (item: any) => {
    if (!item.category) return [];
    return item.viewAll ? item.category : item.category.slice(0, 4);
  };
});

// 工具函数
const formatCountDown = (milliseconds: number) => {
  if (!milliseconds || milliseconds <= 0) return "00天00时00分00秒";

  const totalSeconds = Math.floor(milliseconds / 1000);
  const days = Math.floor(totalSeconds / (24 * 3600));
  const hours = Math.floor((totalSeconds % (24 * 3600)) / 3600);
  const minutes = Math.floor((totalSeconds % 3600) / 60);
  const seconds = totalSeconds % 60;

  return `${days.toString().padStart(2, "0")}天${hours
    .toString()
    .padStart(2, "0")}时${minutes.toString().padStart(2, "0")}分${seconds
    .toString()
    .padStart(2, "0")}秒`;
};

// 启动倒计时功能
const startCountdowns = () => {
  // 清除所有现有的定时器
  clearAllCountdowns();

  // 为限时抢购商品启动倒计时
  limitTimePro.value.forEach((item: any, index: number) => {
    if (item.MilliSeconds > 0) {
      startSingleCountdown(`limit_${index}`, item, "limit");
    }
  });

  // 为拼团商品启动倒计时
  groupPro.value.forEach((item: any, index: number) => {
    if (item.MilliSeconds > 0) {
      startSingleCountdown(`group_${index}`, item, "group");
    }
  });

  // 为预热限时抢购商品启动倒计时
  preLimitTimePro.value.forEach((item: any, index: number) => {
    if (item.MilliSeconds > 0) {
      startSingleCountdown(`preLimit_${index}`, item, "preLimit");
    }
  });

  // 为预热拼团商品启动倒计时
  preGroupPro.value.forEach((item: any, index: number) => {
    if (item.MilliSeconds > 0) {
      startSingleCountdown(`preGroup_${index}`, item, "preGroup");
    }
  });
};

// 启动单个倒计时
const startSingleCountdown = (key: string, item: any, type: string) => {
  const timer = setInterval(() => {
    if (item.MilliSeconds > 1000) {
      item.MilliSeconds -= 1000; // 每秒减少1000毫秒
    } else {
      // 倒计时结束
      item.MilliSeconds = 0;
      clearInterval(timer);
      countdownTimers.value.delete(key);

      // 调用倒计时结束回调
      finished(type);
    }
  }, 1000);

  countdownTimers.value.set(key, timer);
};

// 清除所有倒计时
const clearAllCountdowns = () => {
  countdownTimers.value.forEach((timer) => {
    clearInterval(timer);
  });
  countdownTimers.value.clear();
};

// 初始化系统信息
const initSystemInfo = () => {
  const systemInfo = Taro.getSystemInfoSync();
  statusBarHeight.value = systemInfo.statusBarHeight || 0;
  titleHeight.value = 44; // 默认标题栏高度

  // 获取登录状态
  isLogin.value = !!Taro.getStorageSync("Token");

  // 获取用户信息
  const userInfo = Taro.getStorageSync("userInfo");
  if (userInfo) {
    const parsedUserInfo =
      typeof userInfo === "string" ? JSON.parse(userInfo) : userInfo;
    shopName.value = parsedUserInfo?.ShopName || "";
  }
};

// 初始化页面数据 - 从WePY项目迁移
const initPageData = async () => {
  try {
    initLoading.value = true;
    await Promise.all([
      loadBannerData(),
      loadMenuData(),
      loadNewsData(),
      loadLimitTimeProducts(),
      loadGroupProducts(),
      loadPreProducts(),
      loadCategoryData(),
      loadServiceData(),
    ]);

    // 检查红包
    if (isLogin.value) {
      await checkRedpacket();
    }

    // 启动倒计时
    startCountdowns();
  } catch (error) {
    console.error("页面数据加载失败:", error);
    Taro.showToast({
      title: "数据加载失败",
      icon: "none",
    });
  } finally {
    initLoading.value = false;
  }
};

// 数据加载函数 - 从WePY项目迁移
const loadBannerData = async () => {
  try {
    const response = await homeBanner();
    if (response.data && response.data.d) {
      banner.value = response.data.d;
    }
  } catch (error) {
    console.error("轮播图加载失败:", error);
  }
};

const loadMenuData = async () => {
  try {
    const response = await menuList();
    if (response.data && response.data.d) {
      menu.value = response.data.d;
    }
  } catch (error) {
    console.error("菜单数据加载失败:", error);
  }
};

const loadNewsData = async () => {
  try {
    const response = await NewsListing({ NewsID: "" });
    if (response.data && response.data.d && response.data.d[0]) {
      news.value = response.data.d[0];
    }
  } catch (error) {
    console.error("新闻数据加载失败:", error);
  }
};

const loadLimitTimeProducts = async () => {
  try {
    const response = await LimitedTimeProduct({ Page: 1, PageSize: 4 });
    if (response.data && response.data.d) {
      limitTimePro.value = response.data.d.map((item: any) => ({
        ...item,
        percent: ((item.SalesQty / item.PromoQty) * 100).toFixed(2),
      }));
    }
  } catch (error) {
    console.error("限时抢购数据加载失败:", error);
  }
};

const loadGroupProducts = async () => {
  try {
    const response = await GroupPurchaseProduct({ Page: 1, PageSize: 4 });
    if (response.data && response.data.d) {
      groupPro.value = response.data.d.map((item: any) => ({
        ...item,
        percent: ((item.SalesQty / item.PromoQty) * 100).toFixed(2),
      }));
    }
  } catch (error) {
    console.error("拼团数据加载失败:", error);
  }
};

const loadCategoryData = async () => {
  try {
    const response = await HomePageCountryBrand();
    if (response.data && response.data.d) {
      const data = response.data.d;
      data.forEach((item: any) => {
        item.viewAll = false;
      });
      category.value = data;
    }
  } catch (error) {
    console.error("分类数据加载失败:", error);
  }
};

const loadServiceData = async () => {
  try {
    const response = await ServiceHomePageMainCategory();
    if (response.data && response.data.d) {
      const data = response.data.d;
      data.forEach((item: any) => {
        item.viewAll = false;
      });
      serviceList.value = data;
    }
  } catch (error) {
    console.error("服务数据加载失败:", error);
  }
};

const loadPreProducts = async () => {
  try {
    // 加载预热限时抢购
    const preLimitResponse = await PreLimitedTimeProduct({
      Page: 1,
      PageSize: 4,
    });
    if (preLimitResponse.data && preLimitResponse.data.d) {
      preLimitTimePro.value = preLimitResponse.data.d.map((item: any) => ({
        ...item,
        percent: ((item.SalesQty / item.PromoQty) * 100).toFixed(2),
      }));
    }

    // 加载预热拼团
    const preGroupResponse = await PreGroupPurchaseProduct({
      Page: 1,
      PageSize: 4,
    });
    if (preGroupResponse.data && preGroupResponse.data.d) {
      preGroupPro.value = preGroupResponse.data.d.map((item: any) => ({
        ...item,
        percent: ((item.SalesQty / item.PromoQty) * 100).toFixed(2),
      }));
    }
  } catch (error) {
    console.error("预热商品数据加载失败:", error);
  }
};

const checkRedpacket = async () => {
  try {
    const loginResponse = await CheckLogin();
    if (loginResponse?.data?.d === 1) {
      // 获取未查看红包列表
      const response = await Listing_AngpaoBonusDetail({
        Page: 1,
        PageSize: 10,
        BillNo: "",
        CheckStatus: "N",
      });
      if (response.data && response.data.d) {
        repacketIconShow.value = true;
        redpacketData.value = response.data.d;
      }
    }
  } catch (error) {
    console.error("红包检查失败:", error);
  }
};

// 事件处理函数 - 从WePY项目迁移
const swiperChange = () => {
  banner.value.forEach((item: any, i: number) => {
    if (item.VidUrl) {
      const videoContext = Taro.createVideoContext(String(i));
      videoContext.pause();
    }
  });
};

const onTab = (data: any) => {
  const serviceNames = ["医美整形", "细胞服务", "医疗健康"];
  if (data.HomeMenuDescrip === "热门直播") {
    Taro.showToast({
      title: "暂未开放，敬请期待",
      icon: "none",
    });
  } else if (serviceNames.includes(data.HomeMenuDescrip)) {
    const res = serviceList.value.find(
      (it: any) => it.MainCategoryDescrip === data.HomeMenuDescrip
    );
    if (res) {
      Taro.navigateTo({
        url: `/subpackages/pages/mainCategory?MainCategoryCode=${res.MainCategoryCode}&MainCategoryDescrip=${res.MainCategoryDescrip}`,
      });
    } else {
      Taro.showToast({
        title: "后台未配置此类型",
        icon: "none",
      });
    }
  } else if (data.NavigateUrl) {
    Taro.navigateTo({
      url: data.NavigateUrl,
    });
  }
};

const onPlay = () => {
  autoplay.value = false;
};

const onPauseAndEnd = () => {
  autoplay.value = true;
};

const onClose = () => {
  show.value = false;
};

const onOpenRedpacket = () => {
  repacketIconShow.value = false;
  redpacketShow.value = true;
};

const onCloseRedpacket = () => {
  redpacketShow.value = false;
  // 标记红包已查看
  if (redpacketData.value.length > 0) {
    CheckAngPao({
      BillInfo: redpacketData.value.map((it: any) => it.BillNo).join(","),
    }).catch((error) => {
      console.error("标记红包失败:", error);
    });
  }
};

const onViewAllCategory = (i: number) => {
  const newData = [...category.value];
  newData[i].viewAll = !newData[i].viewAll;
  category.value = newData;
};

const onViewAllService = (i: number) => {
  const newData = [...serviceList.value];
  newData[i].viewAll = !newData[i].viewAll;
  serviceList.value = newData;
};

const onGoClass = (
  CategoryCode: string,
  CategoryDescrip: string,
  isService?: boolean
) => {
  Taro.navigateTo({
    url: `/pages/homeCategory?CategoryCode=${CategoryCode}&CategoryDescrip=${CategoryDescrip}&isService=${
      isService || false
    }`,
  });
};

const handleSearchClick = () => {
  Taro.navigateTo({
    url: "/subpackages/pages/searchPro",
  });
};

const handleLoginClick = () => {
  Taro.navigateTo({
    url: "/subpackages/pages/login2/index",
  });
};

const handleProductClick = (
  item: any,
  isGroup?: boolean,
  groupMembers?: number,
  type?: string,
  promoType?: string
) => {
  const params: any = {
    ProductCode: item.ProductCode,
    isService: item.Product === "False",
  };

  if (isGroup && groupMembers) {
    Object.assign(params, { GroupMembers: groupMembers });
  }

  if (type === "pre") {
    Object.assign(params, {
      ReferNo: item.ReferNo,
      type: "pre",
      PromoType: promoType,
    });
  }

  const queryString = Object.entries(params)
    .map(([key, value]) => `${key}=${value}`)
    .join("&");

  Taro.navigateTo({
    url: `/pages/proDetail?${queryString}`,
  });
};

const handleMoreClick = (type: string) => {
  const routes = {
    limitTime: "/subpackages/pages/limitTime",
    groupBuy: "/subpackages/pages/groupBuy",
    preLimitTime: "/subpackages/pages/preLimitTime",
    preGroupBuy: "/subpackages/pages/preGroupBuy",
  };

  const url = routes[type as keyof typeof routes];
  if (url) {
    Taro.navigateTo({ url });
  }
};

const handlePreOrder = (item: any, type: string) => {
  console.log("预热商品操作:", type, item);
  // 这里可以调用预热商品相关的API
  Taro.showToast({
    title:
      type === "limit"
        ? "预热抢购"
        : type === "group"
        ? "预热拼团"
        : "预热参团",
    icon: "none",
  });
};

const finished = (type: string) => {
  console.log("倒计时结束:", type);
  // 重新加载对应的数据
  switch (type) {
    case "limit":
      loadLimitTimeProducts().then(() => {
        // 重新启动限时抢购倒计时
        limitTimePro.value.forEach((item: any, index: number) => {
          if (item.MilliSeconds > 0) {
            startSingleCountdown(`limit_${index}`, item, "limit");
          }
        });
      });
      break;
    case "group":
      loadGroupProducts().then(() => {
        // 重新启动拼团倒计时
        groupPro.value.forEach((item: any, index: number) => {
          if (item.MilliSeconds > 0) {
            startSingleCountdown(`group_${index}`, item, "group");
          }
        });
      });
      break;
    case "preLimit":
      Promise.all([loadLimitTimeProducts(), loadPreProducts()]).then(() => {
        // 重新启动预热限时抢购倒计时
        preLimitTimePro.value.forEach((item: any, index: number) => {
          if (item.MilliSeconds > 0) {
            startSingleCountdown(`preLimit_${index}`, item, "preLimit");
          }
        });
      });
      break;
    case "preGroup":
      Promise.all([loadGroupProducts(), loadPreProducts()]).then(() => {
        // 重新启动预热拼团倒计时
        preGroupPro.value.forEach((item: any, index: number) => {
          if (item.MilliSeconds > 0) {
            startSingleCountdown(`preGroup_${index}`, item, "preGroup");
          }
        });
      });
      break;
  }
};

// 店铺名称相关函数
const onEditShopName = async () => {
  if (!newShopName.value) {
    Taro.showToast({
      title: "店铺名称不能为空",
      icon: "none",
    });
    return;
  }

  try {
    await UpdateMemberShopName({
      ShopName: newShopName.value,
    });
    Taro.showToast({
      title: "修改成功",
      icon: "success",
    });
    shopName.value = newShopName.value;
    showEditShopName.value = false;
    newShopName.value = "";
  } catch (error) {
    console.error("修改店铺名称失败:", error);
  }
};

const onCancelShopName = () => {
  console.log("取消修改店铺名称");
  showEditShopName.value = false;
  newShopName.value = "";
};

// 添加一个测试函数来打开弹窗
const openEditShopName = () => {
  console.log("打开修改店铺名称弹窗");
  newShopName.value = shopName.value; // 预填充当前店铺名称
  showEditShopName.value = true;
};

// 回到顶部
const goTop = () => {
  Taro.pageScrollTo({
    scrollTop: 0,
    duration: 300,
  });
};

// 页面生命周期处理
onMounted(() => {
  initPageData();
  initSystemInfo();
});

onUnmounted(() => {
  clearAllCountdowns();
});

usePageScroll((e) => {
  floorstatus.value = e.scrollTop > 200;
});

usePullDownRefresh(() => {
  initPageData().finally(() => {
    Taro.stopPullDownRefresh();
  });
});

useDidShow(() => {
  // 更新登录状态
  isLogin.value = !!Taro.getStorageSync("Token");

  // 更新用户信息
  const userInfo = Taro.getStorageSync("userInfo");
  if (userInfo) {
    const parsedUserInfo =
      typeof userInfo === "string" ? JSON.parse(userInfo) : userInfo;
    shopName.value = parsedUserInfo?.ShopName || "";
  }

  // 如果不是初次加载，刷新商品数据
  if (!initLoading.value) {
    Promise.all([
      loadLimitTimeProducts(),
      loadGroupProducts(),
      loadPreProducts(),
    ]).then(() => {
      // 数据加载完成后重新启动倒计时
      startCountdowns();
    });
  }

  // 检查红包
  if (isLogin.value) {
    checkRedpacket();
  }
});
</script>
