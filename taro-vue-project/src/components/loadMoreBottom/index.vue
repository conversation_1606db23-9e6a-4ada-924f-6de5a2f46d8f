<!-- 加载更多组件 - 从WePY2迁移到Taro + Vue3 -->
<template>
  <view class="load-more-container">
    <!-- 加载中状态 -->
    <view v-if="type === 'loading'" class="loading-state">
      <nut-icon name="loading" class="loading-icon" />
      <text class="loading-text">加载中...</text>
    </view>
    
    <!-- 加载完成状态 -->
    <view v-else-if="type === 'done'" class="done-state">
      <text class="done-text">没有更多数据了</text>
    </view>
    
    <!-- 加载失败状态 -->
    <view v-else-if="type === 'error'" class="error-state">
      <text class="error-text">加载失败，</text>
      <text class="retry-text" @tap="onRetry">点击重试</text>
    </view>
    
    <!-- 默认状态 -->
    <view v-else class="default-state">
      <text class="default-text">上拉加载更多</text>
    </view>
  </view>
</template>

<script lang="ts" setup name="LoadMoreBottom">
import { defineProps, defineEmits } from 'vue'

// 定义属性
interface Props {
  type?: 'loading' | 'done' | 'error' | 'default'
}

const props = withDefaults(defineProps<Props>(), {
  type: 'default'
})

// 定义事件
const emit = defineEmits<{
  refresh: []
}>()

// 重试点击事件
const onRetry = () => {
  emit('refresh')
}
</script>

<style lang="less" scoped>
.load-more-container {
  display: flex;
  align-items: center;
  justify-content: center;
  min-height: 80rpx;
}

.loading-state {
  display: flex;
  align-items: center;
  justify-content: center;
  
  .loading-icon {
    margin-right: 16rpx;
    font-size: 32rpx;
    color: #8CDAC6;
    animation: rotate 1s linear infinite;
  }
  
  .loading-text {
    font-size: 28rpx;
    color: #666;
  }
}

.done-state {
  .done-text {
    font-size: 26rpx;
    color: #999;
  }
}

.error-state {
  .error-text {
    font-size: 28rpx;
    color: #999;
  }
  
  .retry-text {
    font-size: 28rpx;
    color: #8CDAC6;
    text-decoration: underline;
  }
}

.default-state {
  .default-text {
    font-size: 28rpx;
    color: #999;
  }
}

@keyframes rotate {
  from {
    transform: rotate(0deg);
  }
  to {
    transform: rotate(360deg);
  }
}
</style>
