<!-- 空状态组件 - 从WePY2迁移到Taro + Vue3 -->
<template>
  <view class="empty-container">
    <view class="empty-icon">
      <image 
        :src="icon || defaultIcon" 
        mode="aspectFit"
        class="icon-image"
      />
    </view>
    <view class="empty-text">{{ text || '暂无数据' }}</view>
    <view v-if="showButton" class="empty-button">
      <nut-button 
        type="primary" 
        size="small"
        @click="onButtonClick"
      >
        {{ buttonText || '重新加载' }}
      </nut-button>
    </view>
  </view>
</template>

<script lang="ts" setup name="Empty">
import { defineProps, defineEmits } from 'vue'

// 定义属性
interface Props {
  text?: string
  icon?: string
  showButton?: boolean
  buttonText?: string
}

const props = withDefaults(defineProps<Props>(), {
  text: '暂无数据',
  icon: '',
  showButton: false,
  buttonText: '重新加载'
})

// 定义事件
const emit = defineEmits<{
  buttonClick: []
}>()

// 默认图标
const defaultIcon = '/static/home/<USER>'

// 按钮点击事件
const onButtonClick = () => {
  emit('buttonClick')
}
</script>

<style lang="less" scoped>
.empty-container {
  display: flex;
  flex-direction: column;
  align-items: center;
  justify-content: center;
  padding: 100rpx 40rpx;
  min-height: 400rpx;
}

.empty-icon {
  margin-bottom: 40rpx;
  
  .icon-image {
    width: 200rpx;
    height: 200rpx;
    opacity: 0.6;
  }
}

.empty-text {
  font-size: 28rpx;
  color: #999;
  margin-bottom: 40rpx;
  text-align: center;
  line-height: 1.5;
}

.empty-button {
  margin-top: 20rpx;
}
</style>
