<template>
  <view
    :class="{ 'list-item': true, 'small-list-item': isSmall }"
    :style="customStyle"
    :hover-class="hover ? 'item-hover' : ''"
    @tap="onTap"
  >
    <image :src="data.Image || '/static/default.jpg'" mode="aspectFill" class="left" />
    <view class="right">
      <view class="name">{{ data.Name || data.ProductDescrip || '商品名称' }}</view>
      <view class="bottom">
        <!-- 市场价 -->
        <view class="price-row1">
          <view class="normal">
            <view class="price">¥{{ data.RetailPrice || data.SalesPrice || '0.00' }}</view>
            <text class="tag">市场价</text>
          </view>
        </view>

        <!-- 会员价（服务商品显示服务确认金时） -->
        <view class="price-row1" v-if="isDeposit">
          <view class="vip">
            <view class="price">
              {{ data.PointProduct === 'True' ? '' : '¥' }}<text>{{ data.MemberPrice || '0.00' }}</text>
            </view>
            <text class="tag">{{ data.PointProduct === 'True' ? '积分' : '会员价' }}</text>
          </view>
        </view>

        <!-- 底部价格和购物车 -->
        <view class="price-row2">
          <!-- 服务确认金 -->
          <view class="earnest" v-if="isDeposit">
            <view class="price">¥{{ data.Deposit || '0.00' }}</view>
            <text class="tag">服务确认金</text>
          </view>
          <!-- 会员价（非服务商品） -->
          <view class="vip" v-if="!isDeposit">
            <view class="price">
              {{ data.PointProduct === 'True' ? '' : '¥' }}<text>{{ data.MemberPrice || '0.00' }}</text>
            </view>
            <text class="tag">{{ data.PointProduct === 'True' ? '积分' : '会员价' }}</text>
          </view>
          <view v-else></view>

          <!-- 购物车按钮 -->
          <image :src="cartIcon" class="cart" v-if="isSmall" @tap.stop="onAddCart" />
          <view class="cart2" v-else @tap.stop="onAddCart">
            <image :src="cartIcon2" />
            <text>加入购物车</text>
          </view>
        </view>
      </view>
    </view>
  </view>
</template>

<script lang="ts" setup name="ProItem">
import { defineProps, defineEmits, computed } from 'vue'

// 静态资源路径
const cartIcon = '/static/home/<USER>'
const cartIcon2 = '/static/home/<USER>'

interface ProductData {
  ProductCode?: string
  ProductDescrip?: string
  Name?: string
  Image?: string
  SalesPrice?: string | number
  RetailPrice?: string | number
  MemberPrice?: string | number
  Deposit?: string | number
  PointProduct?: string
  Product?: string
  FeaturedProduct?: string
  [key: string]: any
}

interface Props {
  data: ProductData
  size?: 'default' | 'small'
  customStyle?: string
  hover?: boolean
}

const props = withDefaults(defineProps<Props>(), {
  data: () => ({}),
  size: 'default',
  customStyle: '',
  hover: true
})

const emit = defineEmits<{
  tap: [data: ProductData]
  addCart: [data: ProductData]
}>()

// 计算属性
const isSmall = computed(() => props.size === 'small')

const isDeposit = computed(() => {
  const { data } = props
  return data && data.Product === 'False' && data.FeaturedProduct === 'False'
})

// 事件处理
const onTap = () => {
  emit('tap', props.data)
}

const onAddCart = () => {
  emit('addCart', props.data)
}
</script>

<style lang="less" scoped>
// 通用混合样式
.flex(@align: center, @justify: flex-start) {
  display: flex;
  align-items: @align;
  justify-content: @justify;
}

.ellipsis-single() {
  overflow: hidden;
  text-overflow: ellipsis;
  white-space: nowrap;
}

.ellipsis-multi(@lines: 2) {
  overflow: hidden;
  text-overflow: ellipsis;
  display: -webkit-box;
  -webkit-line-clamp: @lines;
  -webkit-box-orient: vertical;
}

.btn1-bg() {
  background: linear-gradient(135deg, #129799 0%, #0f7a7c 100%);
  border-radius: 10rpx;
}

.list-item {
  display: flex;
  padding: 30rpx;
  box-sizing: border-box;
  background-color: #fff;
  box-shadow: 0 4rpx 12rpx 1rpx rgba(3, 3, 3, 0.06);
  border-radius: 20rpx;

  .left {
    width: 200rpx;
    height: 200rpx;
    border-radius: 20rpx;
  }

  .right {
    flex: 1;
    display: flex;
    flex-direction: column;
    justify-content: space-between;
    margin-left: 20rpx;

    .name {
      font-size: 32rpx;
      color: #393737;
      font-weight: bold;
      .ellipsis-multi(2);
    }

    .bottom {
      .vip {
        .flex(center, flex-start);
        flex-wrap: wrap;
        .price {
          font-size: 28rpx;
          font-weight: bold;
          color: #129799;
          margin-right: 10rpx;
        }
        .tag {
          line-height: 36rpx;
          text-align: center;
          background: #a8f2ed;
          padding: 0 10rpx;
          border-radius: 8rpx;
          font-size: 20rpx;
          color: #052829;
        }
      }

      .normal {
        .flex(center, flex-start);
        flex-wrap: wrap;
        .price {
          font-size: 28rpx;
          text-decoration: line-through;
          margin-right: 10rpx;
        }
        .tag {
          line-height: 34rpx;
          text-align: center;
          border: 1rpx solid #afafaf;
          padding: 0 10rpx;
          border-radius: 8rpx;
          font-size: 20rpx;
          box-sizing: border-box;
        }
      }

      .earnest {
        .flex(center, center);
        .tag {
          font-size: 20rpx;
          padding: 0 10rpx;
          line-height: 35rpx;
          text-align: center;
          border-radius: 8rpx;
          color: #fff;
          background-color: #428485;
        }
        .price {
          font-size: 28rpx;
          font-weight: bold;
          color: #242d2b;
          margin-right: 10rpx;
        }
      }

      .price-row1 {
        .flex(center, space-between);
        margin-top: 15rpx;
      }

      .price-row2 {
        .flex(center, space-between);
        margin-top: 15rpx;
        .cart2 {
          padding: 10rpx 20rpx;
          display: flex;
          align-items: center;
          justify-content: center;
          font-size: 20rpx;
          font-weight: bold;
          color: #fff;
          .btn1-bg();
          image {
            width: 35rpx;
            height: 35rpx;
            margin-right: 10rpx;
          }
        }
      }

      &:not(:last-child) {
        padding-bottom: 30rpx;
        border-bottom: 1rpx solid #eee;
      }
    }
  }
}

.small-list-item {
  padding: 20rpx;
  .left {
    width: 160rpx;
    height: 160rpx;
    border-radius: 10rpx;
  }
  .right {
    .name {
      font-size: 28rpx;
      font-weight: 400;
      .tag {
        font-size: 18rpx;
      }
    }
    .vip {
      .tag {
        font-size: 16rpx;
        padding: 2rpx 10rpx;
        line-height: 30rpx;
        max-width: 130rpx;
      }
      .price {
        font-size: 24rpx;
        flex: 1;
      }
    }
    .normal {
      .flex(center, center);
      .tag {
        font-size: 16rpx;
        padding: 0 10rpx;
        line-height: 30rpx;
      }
      .price {
        font-size: 24rpx;
      }
    }
    .earnest {
      .tag {
        font-size: 19rpx;
        padding: 0 10rpx;
        line-height: 35rpx;
      }
      .price {
        font-size: 28rpx;
      }
    }
    .price-row2 {
      .cart {
        width: 47rpx;
        height: 47rpx;
      }
    }
  }
}

.item-hover {
  background-color: #eee;
}
</style>
